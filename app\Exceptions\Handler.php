<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Session\TokenMismatchException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {
        // 处理 CSRF Token 过期异常 (419 Page Expired)
        if ($exception instanceof TokenMismatchException) {
            // 如果是 AJAX 请求
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => '页面已过期，请刷新页面重试',
                    'redirect' => route('login')
                ], 419);
            }

            // 普通请求的处理方式
            // 选项1: 重定向到登录页面并显示提示信息（推荐）
            return redirect()->route('login')->with('error', '页面已过期，请重新登录');

            // 选项2: 显示专门的419错误页面（如果需要可以取消注释下面这行，注释上面的重定向）
            // return response()->view('errors.419', [], 419);
        }

        return parent::render($request, $exception);
    }
}
