
body {
  font-feature-settings: "salt";
}

:root {
  --bs-body-bg: #eceff1;
  --bs-body-bg-rgb: 236, 239, 241;
  --pc-heading-color: #343a40;
  --pc-active-background: #e9ecef;
  --pc-sidebar-background: #fff;
  --pc-sidebar-color: #616161;
  --pc-sidebar-color-rgb: 57, 70, 95;
  --pc-sidebar-submenu-border-color: var(--bs-gray-300);
  --pc-sidebar-active-color: #6610f2;
  --pc-sidebar-active-color-rgb: 102, 16, 242;
  --pc-sidebar-shadow: none;
  --pc-sidebar-caption-color: #212121;
  --pc-sidebar-border: none;
  --pc-header-background: #fff;
  --pc-header-color: #616161;
  --pc-header-shadow: none;
  --pc-card-box-shadow: none;
  --pc-header-submenu-background: #ffffff;
  --pc-header-submenu-color: #111936;
}

[data-pc-sidebar-theme=dark] {
  --pc-sidebar-background: #1d2630;
  --pc-sidebar-color: #ffffff;
  --pc-sidebar-color-rgb: 255, 255, 255;
  --pc-sidebar-submenu-border-color: var(--bs-gray-600);
  --pc-sidebar-caption-color: #748892;
}

:root {
  --pc-header-background: rgba(var(--bs-white-rgb), 0.7);
}

section {
  padding: 100px 0;
}

.title {
  margin-bottom: 50px;
}
.title h2 {
  font-weight: 600;
}
.title h5 {
  font-weight: 500;
}

.landing-page {
  overflow-x: hidden;
}
@media (min-width: 1600px) {
  .landing-page .container {
    max-width: 1200px;
  }
}

.navbar {
  position: fixed;
  padding: 16px 0;
  width: 100%;
  z-index: 1030;
  -webkit-backdrop-filter: blur(7px);
          backdrop-filter: blur(7px);
  background-color: var(--pc-header-background);
}
.navbar.top-nav-collapse {
  box-shadow: 0px 12px 24px rgba(27, 46, 94, 0.05);
}
.navbar.default {
  top: 0;
  box-shadow: 0px 0px 24px rgba(27, 46, 94, 0.05);
}

.component-page .navbar {
  z-index: 1025;
}
.component-page .list-group {
  list-style-type: none;
}
.component-page .component-block {
  padding: 100px 0 40px;
}
.component-page .component-live-link a {
  color: #0d6efd;
}
.component-page .component-live-link a:hover {
  text-decoration: underline;
}
.component-page .component-list-card {
  box-shadow: var(--pc-sidebar-shadow);
  width: 100%;
}
.component-page .component-list-card .card-body {
  overflow-y: auto;
  height: calc(100vh - 235px);
}
.component-page .component-list-card .list-group-item {
  position: relative;
  border: none;
  font-weight: 500;
  padding: 12px var(--bs-list-group-item-padding-x);
}
.component-page .component-list-card .list-group-item.list-group-item-action {
  color: var(--pc-sidebar-color);
  border-radius: var(--bs-border-radius);
  width: calc(100% - 20px);
  margin: 2px 10px;
}
.component-page .component-list-card .list-group-item.list-group-item-action:hover {
  color: var(--pc-heading-color);
  background-color: rgba(var(--pc-sidebar-color-rgb), 0.1);
}
.component-page .component-list-card .list-group-item.list-group-item-action.active {
  color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}
@media (min-width: 1200px) {
  .component-page .component-offcanvas {
    position: sticky;
    top: 100px;
    z-index: 1020;
  }
}
@media (max-width: 1199.98px) {
  .component-page .component-offcanvas {
    width: 300px;
  }
}
.component-page .component-offcanvas.show .card {
  border: none;
}
.component-page .component-offcanvas.show .component-list-card {
  margin-bottom: 0;
}
.component-page .component-offcanvas.show .component-list-card .card-body {
  height: calc(100vh - 150px);
}
.component-page .footer {
  padding-top: 60px;
}
.component-page .footer .footer-top {
  padding: 60px 0;
  margin-top: 60px;
  border-top: 1px solid var(--bs-border-color);
  border-bottom: 1px solid var(--bs-border-color);
}
@media (max-width: 991.98px) {
  .component-page .footer {
    padding-top: 40px;
  }
  .component-page .footer .footer-top {
    padding: 40px 0;
    margin-top: 40px;
  }
}
.component-page .footer .footer-bottom {
  padding: 22px 0;
}
.component-page .footer .footer-link a {
  margin: 14px 0;
  display: block;
  transition: all 0.08s cubic-bezier(0.37, 0.24, 0.53, 0.99);
  color: var(--bs-primary);
}
@media (max-width: 575.98px) {
  .component-page .footer .footer-link a {
    margin: 4px 0;
  }
}
.component-page .footer .footer-link a:not(:hover) {
  color: var(--bs-gray-700);
  opacity: 0.9;
}
.component-page .footer .footer-link li:last-child a {
  margin-bottom: 0;
}
.component-page .footer .footer-sos-link a {
  transition: all 0.08s cubic-bezier(0.37, 0.24, 0.53, 0.99);
  color: var(--bs-primary);
}
.component-page .footer .footer-sos-link a:not(:hover) {
  color: var(--bs-body-color);
  opacity: 0.9;
}

@media (max-width: 991.98px) {
  section {
    padding: 40px 0;
  }
}
/* =======================================================================
 ===============     Ui kit copy model style      ====================== */
/* Code examples */
.pc-component {
  position: relative;
}
.pc-component.card-body {
  padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x);
}

/* Modal */
.pc-modal {
  position: fixed;
  z-index: 1099;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #282c34;
  transform-origin: 50% 0;
  transform: scale(0);
  transition: all 0.3s ease-in-out;
}

.pc-modal-opened {
  overflow: hidden !important;
}
.pc-modal-opened .pc-modal {
  transform: scale(1);
}

.pc-component .pc-modal-content {
  margin: 25px -25px -25px;
  padding: 45px 25px 25px;
}
.pc-component.card-body .pc-modal-content {
  margin: 25px -25px -25px;
  padding: 45px 25px 25px;
}

.card-border-outside .pc-modal-content {
  margin-inline: 0;
  margin-bottom: 0;
}

.pc-modal-content {
  max-width: 100vw;
  overflow: auto;
  position: relative;
  border-top: 1px solid var(--bs-border-color);
}
.pc-modal-content > pre {
  overflow: hidden;
  width: 100%;
  margin-bottom: 0;
  margin-top: 20px;
}
.pc-modal-content > pre .hljs::-webkit-scrollbar {
  height: 4px;
  opacity: 0;
}
.pc-modal-content > pre .hljs::-webkit-scrollbar:hover {
  opacity: 1;
}
.pc-modal-content > pre .hljs::-webkit-scrollbar-track {
  background: transparent;
}
.pc-modal-content > pre .hljs::-webkit-scrollbar-thumb {
  background: #dde3e6;
}
.pc-modal-content > pre .hljs::-webkit-scrollbar-thumb:hover {
  background: #a3b1bb;
}
.pc-modal-content > pre > code {
  padding: 0;
  background: none;
  font-size: 16px;
}

.md-pc-modal-copy,
.pc-collapse {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  position: absolute;
  top: 15px;
  border-radius: 20px;
  padding: 0;
  width: 40px;
  height: 40px;
  color: var(--bs-body-color);
  z-index: 1;
  white-space: nowrap;
}
.md-pc-modal-copy:hover,
.pc-collapse:hover {
  color: var(--bs-primary);
  background: var(--bs-primary-light);
}

.pc-collapse {
  right: 25px;
}

.md-pc-modal-copy {
  right: 75px;
}
.md-pc-modal-copy.copied::before {
  content: "Copied!.";
  position: absolute;
  display: block;
  right: 100%;
  margin-right: 10px;
  font-size: 14px;
  color: var(--bs-success);
  background: rgba(var(--bs-success-rgb), 0.1);
  line-height: 24px;
  height: 24px;
  border-radius: var(--bs-border-radius);
  padding: 0 6px;
  top: 50%;
  margin-top: -12px;
}

.pc-modal-close {
  display: block;
  position: fixed;
  top: 10px;
  right: 52px;
  color: #fff;
  opacity: 0.2;
  font-size: 3rem;
  font-weight: 100;
  transition: all 0.3s ease-in-out;
  z-index: 1;
}
.pc-modal-close:hover {
  color: #fff;
  opacity: 0.9;
}

/* Code */
.cui-bottom-spacer {
  height: 12rem;
}

/* editor style for model */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  color: #6e7073;
  background: #fafafa;
}

.hljs-comment,
.hljs-quote {
  color: #a0a1a7;
  font-style: italic;
}

.hljs-doctag,
.hljs-formula,
.hljs-keyword {
  color: #a626a4;
}

.hljs-deletion,
.hljs-name,
.hljs-section,
.hljs-selector-tag,
.hljs-subst {
  color: #e45649;
}

.hljs-literal {
  color: #0184bb;
}

.hljs-addition,
.hljs-attribute,
.hljs-meta-string,
.hljs-regexp,
.hljs-string {
  color: #50a14f;
}

.hljs-built_in,
.hljs-class .hljs-title {
  color: #c18401;
}

.hljs-attr,
.hljs-number,
.hljs-selector-attr,
.hljs-selector-class,
.hljs-selector-pseudo,
.hljs-template-variable,
.hljs-type,
.hljs-variable {
  color: #986801;
}

.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id,
.hljs-symbol,
.hljs-title {
  color: #4078f2;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-link {
  text-decoration: underline;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
