
body {
  font-feature-settings: "salt";
}

:root {
  --bs-body-bg: #eceff1;
  --bs-body-bg-rgb: 236, 239, 241;
  --pc-heading-color: #343a40;
  --pc-active-background: #e9ecef;
  --pc-sidebar-background: #fff;
  --pc-sidebar-color: #616161;
  --pc-sidebar-color-rgb: 57, 70, 95;
  --pc-sidebar-submenu-border-color: var(--bs-gray-300);
  --pc-sidebar-active-color: #6610f2;
  --pc-sidebar-active-color-rgb: 102, 16, 242;
  --pc-sidebar-shadow: none;
  --pc-sidebar-caption-color: #212121;
  --pc-sidebar-border: none;
  --pc-header-background: #fff;
  --pc-header-color: #616161;
  --pc-header-shadow: none;
  --pc-card-box-shadow: none;
  --pc-header-submenu-background: #ffffff;
  --pc-header-submenu-color: #111936;
}

[data-pc-sidebar-theme=dark] {
  --pc-sidebar-background: #1d2630;
  --pc-sidebar-color: #ffffff;
  --pc-sidebar-color-rgb: 255, 255, 255;
  --pc-sidebar-submenu-border-color: var(--bs-gray-600);
  --pc-sidebar-caption-color: #748892;
}

section {
  padding: 100px 0;
}

.title {
  margin-bottom: 50px;
}
.title h2 {
  font-weight: 700;
}

.landing-page {
  overflow-x: hidden;
}
@media (min-width: 1600px) {
  .landing-page .container {
    max-width: 1200px;
  }
}

.navbar {
  position: absolute;
  padding: 16px 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  z-index: 99;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.navbar.top-nav-collapse {
  box-shadow: 0 8px 6px -10px rgba(0, 0, 0, 0.5);
}
.navbar.default {
  top: 0;
  box-shadow: none;
}
.navbar .nav-link {
  font-size: 0.875rem;
  font-weight: 500;
}

header {
  padding: 100px 0;
  display: flex;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(360deg, rgb(238, 242, 246) 1.09%, rgb(255, 255, 255) 100%);
  overflow: hidden;
  position: relative;
}
header:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("../images/landing/img-header-bg.png");
  background-position: right bottom;
  background-size: 80%;
  background-repeat: no-repeat;
  z-index: 1;
}
header > * {
  position: relative;
  z-index: 5;
}

header .hero-image {
  position: relative;
  transform-origin: top left;
  transform: unset;
  -webkit-transform: unset;
  -moz-transform: unset;
  -ms-transform: unset;

  -o-transform: unset;
}

@media (min-width: 1700px) {
    header .hero-image {
        position: relative;
        transform-origin: top left;
        transform: scale(1.6);
        -webkit-transform: scale(1.6);
        -moz-transform: scale(1.6);
        -ms-transform: scale(1.6);
        -o-transform: scale(1.6);
      }
}
header .hero-image .img-bg-grid {
  position: absolute;
  bottom: -20px;
  right: 140px;
  width: 70%;
  z-index: -1;
}
header .hero-image .img-widget-1,
header .hero-image .img-widget-2 {
  filter: drop-shadow(rgba(33, 150, 243, 0.3) 0px 0px 50px);
  position: absolute;
  top: 0;
  left: 0;
}
header .hero-image .img-widget-1 {
  animation: 10s linear 2s infinite normal none running slideY;
}
header .hero-image .img-widget-2 {
  animation: 10s linear 0s infinite normal none running slideY;
}
header h1 {
  /* font-size: 64px; */
  font-size: 45px;
}

.feature-card {
  position: relative;
  overflow: hidden;
}
.feature-card::after, .feature-card::before {
  content: "";
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  z-index: 1;
}
.feature-card::before {
  border: 2px solid rgb(255, 255, 255);
  opacity: 0.21;
  top: -97px;
  right: -3px;
}
.feature-card::after {
  border: 35px solid rgb(255, 255, 255);
  opacity: 0.4;
  top: -72px;
  right: -63px;
}
.feature-card > * {
  position: relative;
  z-index: 5;
}
.feature-card .avtar {
  background: rgb(255, 255, 255);
  opacity: 0.5;
}
.feature-card .avtar img {
  width: 36px;
}

.customize-list li {
  padding: 10px 0px;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.7rem;
}

.application-slider {
  overflow: hidden;
}
.application-slider.bg-primary {
  --bs-bg-opacity: 0.07;
}

.owl-theme .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent;
}
.owl-theme .owl-dots .owl-dot {
  display: inline-block;
  zoom: 1;
}
.owl-theme .owl-dots .owl-dot span {
  width: 10px;
  height: 10px;
  margin: 5px 7px;
  background: rgba(var(--bs-body-color-rgb), 0.3);
  display: block;
  -webkit-backface-visibility: visible;
  transition: opacity 0.2s ease;
  border-radius: 30px;
}
.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
  background: rgba(var(--bs-body-color-rgb), 0.6);
}

.app-slider.swiper {
  overflow: visible;
}
.app-slider .avtar {
  width: 65px;
  height: 65px;
  border-radius: 50%;
  font-size: 36px;
  box-shadow: 0px 24px 38px rgba(28, 28, 29, 0.1);
  background: #ffffff;
  color: var(--bs-body-color);
}
.app-slider .avtar::after {
  display: none;
}
.app-slider .avtar.swiper-button-next {
  transform: translatex(150%);
}
.app-slider .avtar.swiper-button-prev {
  transform: translatex(-150%);
}
.app-slider img {
  transform: scale(0.8);
  border-radius: var(--bs-border-radius);
  margin: 35px 0;
  transition: all 0.2s ease-in-out;
}
.app-slider .swiper-slide {
  opacity: 0.5;
  transition: all 0.2s ease-in-out;
}
.app-slider .swiper-slide.swiper-slide-active {
  opacity: 1;
}
.app-slider .swiper-slide.swiper-slide-active img {
  transform: scale(1);
  box-shadow: 0px 24px 38px rgba(28, 28, 29, 0.1);
}

.testaments-cards {
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
  orphans: 1;
  widows: 1;
  -moz-column-count: 1;
       column-count: 1;
  position: relative;
}
@media (min-width: 576px) {
  .testaments-cards {
    -moz-column-count: 2;
         column-count: 2;
  }
}
@media (min-width: 992px) {
  .testaments-cards {
    -moz-column-count: 3;
         column-count: 3;
  }
}
@media (min-width: 1200px) {
  .testaments-cards {
    -moz-column-count: 4;
         column-count: 4;
  }
}
.testaments-cards::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgb(255, 255, 255), transparent);
  height: 50%;
}
.testaments-cards .card {
  border: 1px solid var(--bs-border-color);
  display: inline-block;
  width: 100%;
}

.choose-section {
  padding-bottom: 0;
  padding-top: 30px;
}
.choose-section .hand-img {
  width: 220px;
  filter: drop-shadow(rgba(33, 150, 243, 0.3) 0px 0px 50px);
}
.choose-section h2 {
  font-size: 2.5rem;
}

.choose-slider {
  position: relative;
  width: 100%;
  height: 200px;
}
.choose-slider .swiper-slide {
  text-align: center;
  font-size: 18px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 50px !important;
  opacity: 0.08;
  transition: all 0.2s ease-in-out;
}
.choose-slider .swiper-slide h2 {
  margin-bottom: 0;
  color: #fff;
}
.choose-slider .swiper-slide.swiper-slide-prev, .choose-slider .swiper-slide.swiper-slide-next {
  opacity: 0.2;
}
.choose-slider .swiper-slide.swiper-slide-active {
  opacity: 1;
}
.choose-slider .swiper-slide.swiper-slide-active h2 {
  color: var(--bs-primary);
}

.frameworks-card {
  background: var(--bs-body-bg);
  text-align: center;
  position: relative;
  margin: 10px 0 35px;
}
.frameworks-card:hover {
  background: rgba(var(--bs-primary-rgb), 0.08);
  box-shadow: 0 4px 15px 0 rgba(var(--bs-primary-rgb), 0.15);
}
.frameworks-card .frameworks-logo {
  width: auto;
  height: 48px;
  margin-bottom: 16px;
}
.frameworks-card .frameworks-badge {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 500;
  background: var(--bs-card-bg);
  border: 1px solid var(--bs-primary);
  color: var(--bs-primary);
  border-radius: 30px;
  padding: 0 5px;
  white-space: nowrap;
}

.frameworks-section {
  overflow: hidden;
}
.frameworks-section .frameworks-slider.swiper {
  overflow: visible;
}

.footer {
  padding: 100px 0 0;
}
.footer .sub-footer {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px 0px;
  margin-top: 100px;
}
.footer .footer-link a {
  display: inline-flex;
  align-items: center;
  transition: all 0.1s ease-in-out;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 20px;
}
.footer .footer-link a i {
  font-size: 20px;
  margin-right: 5px;
}
.footer .footer-link a:hover {
  color: rgb(255, 255, 255);
}

@keyframes slideY {
  0%, 50%, 100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-10px);
  }
  75% {
    transform: translateY(10px);
  }
}
@keyframes updown {
  50% {
    transform: translateY(-40px);
  }
  100% {
    transform: translateY(0);
  }
}
@media (max-width: 991.98px) {
  header {
    text-align: center;
  }
  section {
    padding: 40px 0;
  }
  .choose-section h2 {
    font-size: 1.5rem;
  }
}
@media (max-width: 767.98px) {
  header {
    padding: 100px 0 50px;
  }
  header h1 {
    font-size: 30px;
  }
  .app-slider .avtar {
    display: none;
  }
  .choose-section {
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .footer .footer-link a {
    margin-top: 8px;
  }
}
@media (max-width: 575.98px) {
  .choose-section h2 {
    font-size: 1.1rem;
  }
  .choose-slider {
    height: 120px;
  }
  .choose-slider .swiper-slide {
    height: 30px !important;
  }
}
[data-pc-direction=rtl] header .hero-image {
  transform-origin: top right;
}

[data-pc-theme=dark] header {
  background: rgb(17, 25, 54);
}
[data-pc-theme=dark][data-pc-preset=preset-1] .app-slider .avtar {
  background: #111936;
}
[data-pc-theme=dark][data-pc-preset=preset-1] .navbar.navbar-light .navbar-toggler-icon {
  filter: invert(1) grayscale(100%) brightness(200%);
}
[data-pc-theme=dark][data-pc-preset=preset-1] .navbar.navbar-light:not(.card) {
  background: rgba(17, 25, 54, 0.8);
}
[data-pc-theme=dark][data-pc-preset=preset-2] .app-slider .avtar {
  background: #0e1b23;
}
[data-pc-theme=dark][data-pc-preset=preset-2] .navbar.navbar-light .navbar-toggler-icon {
  filter: invert(1) grayscale(100%) brightness(200%);
}
[data-pc-theme=dark][data-pc-preset=preset-2] .navbar.navbar-light:not(.card) {
  background: rgba(14, 27, 35, 0.8);
}
[data-pc-theme=dark][data-pc-preset=preset-3] .app-slider .avtar {
  background: #0a0f23;
}
[data-pc-theme=dark][data-pc-preset=preset-3] .navbar.navbar-light .navbar-toggler-icon {
  filter: invert(1) grayscale(100%) brightness(200%);
}
[data-pc-theme=dark][data-pc-preset=preset-3] .navbar.navbar-light:not(.card) {
  background: rgba(10, 15, 35, 0.8);
}
[data-pc-theme=dark][data-pc-preset=preset-4] .app-slider .avtar {
  background: #010606;
}
[data-pc-theme=dark][data-pc-preset=preset-4] .navbar.navbar-light .navbar-toggler-icon {
  filter: invert(1) grayscale(100%) brightness(200%);
}
[data-pc-theme=dark][data-pc-preset=preset-4] .navbar.navbar-light:not(.card) {
  background: rgba(1, 6, 6, 0.8);
}
[data-pc-theme=dark][data-pc-preset=preset-5] .app-slider .avtar {
  background: #030708;
}
[data-pc-theme=dark][data-pc-preset=preset-5] .navbar.navbar-light .navbar-toggler-icon {
  filter: invert(1) grayscale(100%) brightness(200%);
}
[data-pc-theme=dark][data-pc-preset=preset-5] .navbar.navbar-light:not(.card) {
  background: rgba(3, 7, 8, 0.8);
}
[data-pc-theme=dark][data-pc-preset=preset-6] .app-slider .avtar {
  background: #051327;
}
[data-pc-theme=dark][data-pc-preset=preset-6] .navbar.navbar-light .navbar-toggler-icon {
  filter: invert(1) grayscale(100%) brightness(200%);
}
[data-pc-theme=dark][data-pc-preset=preset-6] .navbar.navbar-light:not(.card) {
  background: rgba(5, 19, 39, 0.8);
}
[data-pc-theme=dark][data-pc-preset=preset-7] .app-slider .avtar {
  background: #1a223f;
}
[data-pc-theme=dark][data-pc-preset=preset-7] .navbar.navbar-light .navbar-toggler-icon {
  filter: invert(1) grayscale(100%) brightness(200%);
}
[data-pc-theme=dark][data-pc-preset=preset-7] .navbar.navbar-light:not(.card) {
  background: rgba(26, 34, 63, 0.8);
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImxhbmRpbmcuc2NzcyIsInNldHRpbmdzL19ib290c3RyYXAtdmFyaWFibGVzLnNjc3MiLCJsYW5kaW5nLmNzcyIsInNldHRpbmdzL190aGVtZS12YXJpYWJsZXMuc2NzcyIsInNldHRpbmdzL19jb2xvci12YXJpYWJsZXMuc2NzcyIsIi4uLy4uLy4uL25vZGVfbW9kdWxlcy9ib290c3RyYXAvc2Nzcy9taXhpbnMvX2JyZWFrcG9pbnRzLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7Ozs7cUZBQUE7QUNxUkE7RUFDRSw2QkFBQTtBQzVRRjs7QUNQQTtFQUVFLHFCQUFBO0VBQ0EsK0JBQUE7RUFFQSwyQkFBQTtFQUNBLCtCQUFBO0VBR0EsNkJBQUE7RUFDQSwyQkFBQTtFQUNBLGtDQUFBO0VBQ0EscURBQUE7RUFDQSxrQ0FBQTtFQUNBLDJDQUFBO0VBQ0EseUJBQUE7RUFDQSxtQ0FBQTtFQUNBLHlCQUFBO0VBR0EsNEJBQUE7RUFDQSwwQkFBQTtFQUNBLHdCQUFBO0VBR0EsMEJBQUE7RUFHQSx1Q0FBQTtFQUNBLGtDQUFBO0FEQUY7O0FDRUE7RUFFRSxnQ0FBQTtFQUNBLDJCQUFBO0VBQ0EscUNBQUE7RUFDQSxxREFBQTtFQUNBLG1DQUFBO0FEQUY7O0FGbkJBO0VBQ0UsZ0JBQUE7QUVzQkY7O0FGcEJBO0VBQ0UsbUJBQUE7QUV1QkY7QUZ0QkU7RUFDRSxnQkFBQTtBRXdCSjs7QUZyQkE7RUFDRSxrQkFBQTtBRXdCRjtBRnZCRTtFQUNFO0lBQ0UsaUJBQUE7RUV5Qko7QUFDRjs7QUZ0QkE7RUFDRSxlQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxvQ0FBQTtFQUNBLFdBQUE7RUFDQSxrQ0FBQTtVQUFBLDBCQUFBO0FFeUJGO0FGeEJFO0VBQ0UsOENBQUE7QUUwQko7QUZ2QkU7RUFDRSxNQUFBO0VBQ0EsZ0JBQUE7QUV5Qko7QUZ2QkU7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0FFeUJKOztBRnRCQTtFQUNFLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSxzRkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUV5QkY7QUZ4QkU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsNERBQUE7RUFDQSxpQ0FBQTtFQUNBLG9CQUFBO0VBQ0EsNEJBQUE7RUFFQSxVQUFBO0FFeUJKO0FGdkJFO0VBQ0Usa0JBQUE7RUFDQSxVQUFBO0FFeUJKO0FGdkJFO0VBQ0Usa0JBQUE7RUFDQSwwQkFBQTtFQUNBLHFCQUFBO0FFeUJKO0FGeEJJO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0FFMEJOO0FGeEJJOztFQUVFLHlEQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtBRTBCTjtBRnhCSTtFQUNFLDREQUFBO0FFMEJOO0FGeEJJO0VBQ0UsNERBQUE7QUUwQk47QUZ2QkU7RUFDRSxlQUFBO0FFeUJKOztBRnRCQTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7QUV5QkY7QUZ4QkU7RUFFRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLGtCQUFBO0VBQ0EsVUFBQTtBRXlCSjtBRnZCRTtFQUNFLG9DQUFBO0VBQ0EsYUFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0FFeUJKO0FGdkJFO0VBQ0UscUNBQUE7RUFDQSxZQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7QUV5Qko7QUZ2QkU7RUFDRSxrQkFBQTtFQUNBLFVBQUE7QUV5Qko7QUZ2QkU7RUFDRSw4QkFBQTtFQUNBLFlBQUE7QUV5Qko7QUZ4Qkk7RUFDRSxXQUFBO0FFMEJOOztBRnBCRTtFQUNFLGlCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUV1Qko7O0FGcEJBO0VBQ0UsZ0JBQUE7QUV1QkY7QUZ0QkU7RUFDRSxxQkFBQTtBRXdCSjs7QUZuQkU7RUFDRSxrQkFBQTtFQUNBLHdDQUFBO0FFc0JKO0FGckJJO0VBQ0UscUJBQUE7RUFDQSxPQUFBO0FFdUJOO0FGdEJNO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EsK0NBQUE7RUFDQSxjQUFBO0VBQ0Esb0NBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQUFBO0FFd0JSO0FGckJJOztFQUVFLCtDQUFBO0FFdUJOOztBRmpCRTtFQUNFLGlCQUFBO0FFb0JKO0FGbEJFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSwrQ0FBQTtFQUNBLG1CSWxCSTtFSm1CSiwyQkFBQTtBRW9CSjtBRm5CSTtFQUNFLGFBQUE7QUVxQk47QUZuQkk7RUFDRSwyQkFBQTtBRXFCTjtBRm5CSTtFQUNFLDRCQUFBO0FFcUJOO0FGbEJFO0VBQ0UscUJBQUE7RUFDQSxzQ0FBQTtFQUNBLGNBQUE7RUFDQSxnQ0FBQTtBRW9CSjtBRmpCRTtFQUNFLFlBQUE7RUFDQSxnQ0FBQTtBRW1CSjtBRmxCSTtFQUNFLFVBQUE7QUVvQk47QUZuQk07RUFDRSxtQkFBQTtFQUNBLCtDQUFBO0FFcUJSOztBRmZBO0VBQ0Usd0JBQUE7T0FBQSxtQkFBQTtFQUNBLFVBQUE7RUFDQSxTQUFBO0VBQ0Esb0JBQUE7T0FBQSxlQUFBO0VBQ0Esa0JBQUE7QUVrQkY7QUdqTUk7RUwwS0o7SUFPSSxvQkFBQTtTQUFBLGVBQUE7RUVvQkY7QUFDRjtBR3RNSTtFTDBLSjtJQVVJLG9CQUFBO1NBQUEsZUFBQTtFRXNCRjtBQUNGO0FHM01JO0VMMEtKO0lBYUksb0JBQUE7U0FBQSxlQUFBO0VFd0JGO0FBQ0Y7QUZ2QkU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxrRUFBQTtFQUNBLFdBQUE7QUV5Qko7QUZ2QkU7RUFDRSx3Q0FBQTtFQUNBLHFCQUFBO0VBQ0EsV0FBQTtBRXlCSjs7QUZ0QkE7RUFDRSxpQkFBQTtFQUNBLGlCQUFBO0FFeUJGO0FGeEJFO0VBQ0UsWUFBQTtFQUNBLHlEQUFBO0FFMEJKO0FGeEJFO0VBQ0UsaUJBQUE7QUUwQko7O0FGdkJBO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0VBQ0EsYUFBQTtBRTBCRjtBRnpCRTtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSwyQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxhQUFBO0VBQ0EsZ0NBQUE7QUUyQko7QUYxQkk7RUFDRSxnQkFBQTtFQUNBLFdBQUE7QUU0Qk47QUYxQkk7RUFFRSxZQUFBO0FFMkJOO0FGekJJO0VBQ0UsVUFBQTtBRTJCTjtBRjFCTTtFQUNFLHdCQUFBO0FFNEJSOztBRnZCQTtFQUNFLDZCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0FFMEJGO0FGekJFO0VBQ0UsNkNBQUE7RUFDQSwwREFBQTtBRTJCSjtBRnpCRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7QUUyQko7QUZ6QkU7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxTQUFBO0VBQ0EsZ0NBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsNkJBQUE7RUFDQSxtQ0FBQTtFQUNBLHdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7QUUyQko7O0FGeEJBO0VBQ0UsZ0JBQUE7QUUyQkY7QUYxQkU7RUFDRSxpQkFBQTtBRTRCSjs7QUZ4QkE7RUFDRSxrQkFBQTtBRTJCRjtBRjFCRTtFQUNFLHFDQUFBO0VBQ0EsaUJBQUE7RUFDQSxpQkFBQTtBRTRCSjtBRnpCSTtFQUNFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQ0FBQTtFQUNBLCtCQUFBO0VBQ0EsZ0JBQUE7QUUyQk47QUYxQk07RUFDRSxlQUFBO0VBQ0EsaUJBQUE7QUU0QlI7QUYxQk07RUFDRSx5QkFBQTtBRTRCUjs7QUZ2QkE7RUFDRTtJQUdFLHdCQUFBO0VFd0JGO0VGdEJBO0lBQ0UsNEJBQUE7RUV3QkY7RUZ0QkE7SUFDRSwyQkFBQTtFRXdCRjtBQUNGO0FGdEJBO0VBQ0U7SUFDRSw0QkFBQTtFRXdCRjtFRnRCQTtJQUNFLHdCQUFBO0VFd0JGO0FBQ0Y7QUZ0QkE7RUFDRTtJQUNFLGtCQUFBO0VFd0JGO0VGdEJBO0lBQ0UsZUFBQTtFRXdCRjtFRnRCQTtJQUNFLGlCQUFBO0VFd0JGO0FBQ0Y7QUZ0QkE7RUFDRTtJQUNFLHFCQUFBO0VFd0JGO0VGdkJFO0lBQ0UsZUFBQTtFRXlCSjtFRnJCRTtJQUNFLGFBQUE7RUV1Qko7RUZwQkE7SUFDRSxpQkFBQTtJQUNBLG9CQUFBO0VFc0JGO0VGcEJBO0lBQ0UsZUFBQTtFRXNCRjtBQUNGO0FGcEJBO0VBQ0U7SUFDRSxpQkFBQTtFRXNCRjtFRnBCQTtJQUNFLGFBQUE7RUVzQkY7RUZyQkU7SUFDRSx1QkFBQTtFRXVCSjtBQUNGO0FGbkJFO0VBQ0UsMkJBQUE7QUVxQko7O0FGakJFO0VBQ0UsMkJBQUE7QUVvQko7QUZUTTtFQUNFLG1CQUZRO0FFYWhCO0FGUlE7RUFDRSxrREFBQTtBRVVWO0FGUlE7RUFDRSxpQ0FBQTtBRVVWO0FGbEJNO0VBQ0UsbUJBRlE7QUVzQmhCO0FGakJRO0VBQ0Usa0RBQUE7QUVtQlY7QUZqQlE7RUFDRSxpQ0FBQTtBRW1CVjtBRjNCTTtFQUNFLG1CQUZRO0FFK0JoQjtBRjFCUTtFQUNFLGtEQUFBO0FFNEJWO0FGMUJRO0VBQ0UsaUNBQUE7QUU0QlY7QUZwQ007RUFDRSxtQkFGUTtBRXdDaEI7QUZuQ1E7RUFDRSxrREFBQTtBRXFDVjtBRm5DUTtFQUNFLDhCQUFBO0FFcUNWO0FGN0NNO0VBQ0UsbUJBRlE7QUVpRGhCO0FGNUNRO0VBQ0Usa0RBQUE7QUU4Q1Y7QUY1Q1E7RUFDRSw4QkFBQTtBRThDVjtBRnRETTtFQUNFLG1CQUZRO0FFMERoQjtBRnJEUTtFQUNFLGtEQUFBO0FFdURWO0FGckRRO0VBQ0UsZ0NBQUE7QUV1RFY7QUYvRE07RUFDRSxtQkFGUTtBRW1FaEI7QUY5RFE7RUFDRSxrREFBQTtBRWdFVjtBRjlEUTtFQUNFLGlDQUFBO0FFZ0VWIiwiZmlsZSI6ImxhbmRpbmcuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxyXG49PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XHJcblRlbXBsYXRlIE5hbWU6IEJlcnJ5IC0gQm9vdHN0cmFwIEFkbWluIFRlbXBsYXRlXHJcbkF1dGhvcjogY29kZWR0aGVtZXNcclxuU3VwcG9ydDogaHR0cHM6Ly9jb2RlZHRoZW1lcy5hdXRob3JkZXNrLmFwcFxyXG5GaWxlOiBzdHlsZS5jc3NcclxuPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxyXG49PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSAqL1xyXG5cclxuLy8gbWFpbiBmcmFtZXdvcmtcclxuQGltcG9ydCAnc2V0dGluZ3MvY29sb3ItdmFyaWFibGVzJztcclxuQGltcG9ydCAnbm9kZV9tb2R1bGVzL2Jvb3RzdHJhcC9zY3NzL2Z1bmN0aW9ucyc7XHJcbkBpbXBvcnQgJ25vZGVfbW9kdWxlcy9ib290c3RyYXAvc2Nzcy92YXJpYWJsZXMnO1xyXG5AaW1wb3J0ICdub2RlX21vZHVsZXMvYm9vdHN0cmFwL3Njc3MvdmFyaWFibGVzLWRhcmsnO1xyXG5AaW1wb3J0ICdub2RlX21vZHVsZXMvYm9vdHN0cmFwL3Njc3MvbWl4aW5zJztcclxuJHBjLWJvZHktYmc6IG1hcC1nZXQoJHByZXNldC1jb2xvcnMsICdwcmVzZXQtMScsICdib2R5Jyk7XHJcbiRwYy1ibHVlOiBtYXAtZ2V0KCRwcmVzZXQtY29sb3JzLCAncHJlc2V0LTEnLCAncHJpbWFyeScpO1xyXG4kcGMtc2Vjb25kYXJ5OiBtYXAtZ2V0KCRwcmVzZXQtY29sb3JzLCAncHJlc2V0LTEnLCAnc2Vjb25kYXJ5Jyk7XHJcbkBpbXBvcnQgJ3NldHRpbmdzL2Jvb3RzdHJhcC12YXJpYWJsZXMnO1xyXG5AaW1wb3J0ICdzZXR0aW5ncy90aGVtZS12YXJpYWJsZXMnO1xyXG5cclxuc2VjdGlvbiB7XHJcbiAgcGFkZGluZzogMTAwcHggMDtcclxufVxyXG4udGl0bGUge1xyXG4gIG1hcmdpbi1ib3R0b206IDUwcHg7XHJcbiAgaDIge1xyXG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICB9XHJcbn1cclxuLmxhbmRpbmctcGFnZSB7XHJcbiAgb3ZlcmZsb3cteDogaGlkZGVuO1xyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAxNjAwcHgpIHtcclxuICAgIC5jb250YWluZXIge1xyXG4gICAgICBtYXgtd2lkdGg6IDEyMDBweDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuLm5hdmJhciB7XHJcbiAgcG9zaXRpb246IGZpeGVkO1xyXG4gIHBhZGRpbmc6IDE2cHggMDtcclxuICB3aWR0aDogMTAwJTtcclxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7XHJcbiAgei1pbmRleDogOTk7XHJcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDVweCk7XHJcbiAgJi50b3AtbmF2LWNvbGxhcHNlIHtcclxuICAgIGJveC1zaGFkb3c6IDAgOHB4IDZweCAtMTBweCByZ2JhKDAsIDAsIDAsIDAuNSk7XHJcbiAgfVxyXG5cclxuICAmLmRlZmF1bHQge1xyXG4gICAgdG9wOiAwO1xyXG4gICAgYm94LXNoYWRvdzogbm9uZTtcclxuICB9XHJcbiAgLm5hdi1saW5rIHtcclxuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICBmb250LXdlaWdodDogNTAwO1xyXG4gIH1cclxufVxyXG5oZWFkZXIge1xyXG4gIHBhZGRpbmc6IDEwMHB4IDA7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgzNjBkZWcsIHJnYigyMzgsIDI0MiwgMjQ2KSAxLjA5JSwgcmdiKDI1NSwgMjU1LCAyNTUpIDEwMCUpO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICY6YWZ0ZXIge1xyXG4gICAgY29udGVudDogJyc7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICB0b3A6IDA7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgcmlnaHQ6IDA7XHJcbiAgICBib3R0b206IDA7XHJcbiAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoJy4uL2ltYWdlcy9sYW5kaW5nL2ltZy1oZWFkZXItYmcucG5nJyk7XHJcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiByaWdodCBib3R0b207XHJcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDgwJTtcclxuICAgIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XHJcblxyXG4gICAgei1pbmRleDogMTtcclxuICB9XHJcbiAgPiAqIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIHotaW5kZXg6IDU7XHJcbiAgfVxyXG4gIC5oZXJvLWltYWdlIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIHRyYW5zZm9ybS1vcmlnaW46IHRvcCBsZWZ0O1xyXG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjYpO1xyXG4gICAgLmltZy1iZy1ncmlkIHtcclxuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICBib3R0b206IC0yMHB4O1xyXG4gICAgICByaWdodDogMTQwcHg7XHJcbiAgICAgIHdpZHRoOiA3MCU7XHJcbiAgICAgIHotaW5kZXg6IC0xO1xyXG4gICAgfVxyXG4gICAgLmltZy13aWRnZXQtMSxcclxuICAgIC5pbWctd2lkZ2V0LTIge1xyXG4gICAgICBmaWx0ZXI6IGRyb3Atc2hhZG93KHJnYmEoMzMsIDE1MCwgMjQzLCAwLjMpIDBweCAwcHggNTBweCk7XHJcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgdG9wOiAwO1xyXG4gICAgICBsZWZ0OiAwO1xyXG4gICAgfVxyXG4gICAgLmltZy13aWRnZXQtMSB7XHJcbiAgICAgIGFuaW1hdGlvbjogMTBzIGxpbmVhciAycyBpbmZpbml0ZSBub3JtYWwgbm9uZSBydW5uaW5nIHNsaWRlWTtcclxuICAgIH1cclxuICAgIC5pbWctd2lkZ2V0LTIge1xyXG4gICAgICBhbmltYXRpb246IDEwcyBsaW5lYXIgMHMgaW5maW5pdGUgbm9ybWFsIG5vbmUgcnVubmluZyBzbGlkZVk7XHJcbiAgICB9XHJcbiAgfVxyXG4gIGgxIHtcclxuICAgIGZvbnQtc2l6ZTogNjRweDtcclxuICB9XHJcbn1cclxuLmZlYXR1cmUtY2FyZCB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgJjo6YWZ0ZXIsXHJcbiAgJjo6YmVmb3JlIHtcclxuICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgd2lkdGg6IDE1MHB4O1xyXG4gICAgaGVpZ2h0OiAxNTBweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgIHotaW5kZXg6IDE7XHJcbiAgfVxyXG4gICY6OmJlZm9yZSB7XHJcbiAgICBib3JkZXI6IDJweCBzb2xpZCByZ2IoMjU1LCAyNTUsIDI1NSk7XHJcbiAgICBvcGFjaXR5OiAwLjIxO1xyXG4gICAgdG9wOiAtOTdweDtcclxuICAgIHJpZ2h0OiAtM3B4O1xyXG4gIH1cclxuICAmOjphZnRlciB7XHJcbiAgICBib3JkZXI6IDM1cHggc29saWQgcmdiKDI1NSwgMjU1LCAyNTUpO1xyXG4gICAgb3BhY2l0eTogMC40O1xyXG4gICAgdG9wOiAtNzJweDtcclxuICAgIHJpZ2h0OiAtNjNweDtcclxuICB9XHJcbiAgPiAqIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIHotaW5kZXg6IDU7XHJcbiAgfVxyXG4gIC5hdnRhciB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDEpO1xyXG4gICAgb3BhY2l0eTogMC41O1xyXG4gICAgaW1nIHtcclxuICAgICAgd2lkdGg6IDM2cHg7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4uY3VzdG9taXplLWxpc3Qge1xyXG4gIGxpIHtcclxuICAgIHBhZGRpbmc6IDEwcHggMHB4O1xyXG4gICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBnYXA6IDAuN3JlbTtcclxuICB9XHJcbn1cclxuLmFwcGxpY2F0aW9uLXNsaWRlciB7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAmLmJnLXByaW1hcnkge1xyXG4gICAgLS1icy1iZy1vcGFjaXR5OiAwLjA3O1xyXG4gIH1cclxufVxyXG5cclxuLm93bC10aGVtZSB7XHJcbiAgLm93bC1kb3RzIHtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgIC13ZWJraXQtdGFwLWhpZ2hsaWdodC1jb2xvcjogdHJhbnNwYXJlbnQ7XHJcbiAgICAub3dsLWRvdCB7XHJcbiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICAgICAgem9vbTogMTtcclxuICAgICAgc3BhbiB7XHJcbiAgICAgICAgd2lkdGg6IDEwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiAxMHB4O1xyXG4gICAgICAgIG1hcmdpbjogNXB4IDdweDtcclxuICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKHZhcigtLWJzLWJvZHktY29sb3ItcmdiKSwgMC4zKTtcclxuICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICAtd2Via2l0LWJhY2tmYWNlLXZpc2liaWxpdHk6IHZpc2libGU7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjJzIGVhc2U7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMzBweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgLm93bC1kb3QuYWN0aXZlIHNwYW4sXHJcbiAgICAub3dsLWRvdDpob3ZlciBzcGFuIHtcclxuICAgICAgYmFja2dyb3VuZDogcmdiYSh2YXIoLS1icy1ib2R5LWNvbG9yLXJnYiksIDAuNik7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4uYXBwLXNsaWRlciB7XHJcbiAgJi5zd2lwZXIge1xyXG4gICAgb3ZlcmZsb3c6IHZpc2libGU7XHJcbiAgfVxyXG4gIC5hdnRhciB7XHJcbiAgICB3aWR0aDogNjVweDtcclxuICAgIGhlaWdodDogNjVweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgIGZvbnQtc2l6ZTogMzZweDtcclxuICAgIGJveC1zaGFkb3c6IDBweCAyNHB4IDM4cHggcmdiYSgyOCwgMjgsIDI5LCAwLjEpO1xyXG4gICAgYmFja2dyb3VuZDogJHdoaXRlO1xyXG4gICAgY29sb3I6IHZhcigtLWJzLWJvZHktY29sb3IpO1xyXG4gICAgJjo6YWZ0ZXIge1xyXG4gICAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgfVxyXG4gICAgJi5zd2lwZXItYnV0dG9uLW5leHQge1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZXgoMTUwJSk7XHJcbiAgICB9XHJcbiAgICAmLnN3aXBlci1idXR0b24tcHJldiB7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRleCgtMTUwJSk7XHJcbiAgICB9XHJcbiAgfVxyXG4gIGltZyB7XHJcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDAuOCk7XHJcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1icy1ib3JkZXItcmFkaXVzKTtcclxuICAgIG1hcmdpbjogMzVweCAwO1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XHJcbiAgfVxyXG5cclxuICAuc3dpcGVyLXNsaWRlIHtcclxuICAgIG9wYWNpdHk6IDAuNTtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UtaW4tb3V0O1xyXG4gICAgJi5zd2lwZXItc2xpZGUtYWN0aXZlIHtcclxuICAgICAgb3BhY2l0eTogMTtcclxuICAgICAgaW1nIHtcclxuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEpO1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDBweCAyNHB4IDM4cHggcmdiYSgyOCwgMjgsIDI5LCAwLjEpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4udGVzdGFtZW50cy1jYXJkcyB7XHJcbiAgY29sdW1uLWdhcDogMS4yNXJlbTtcclxuICBvcnBoYW5zOiAxO1xyXG4gIHdpZG93czogMTtcclxuICBjb2x1bW4tY291bnQ6IDE7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAoc20pIHtcclxuICAgIGNvbHVtbi1jb3VudDogMjtcclxuICB9XHJcbiAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cChsZykge1xyXG4gICAgY29sdW1uLWNvdW50OiAzO1xyXG4gIH1cclxuICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKHhsKSB7XHJcbiAgICBjb2x1bW4tY291bnQ6IDQ7XHJcbiAgfVxyXG4gICY6OmFmdGVyIHtcclxuICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgYm90dG9tOiAwO1xyXG4gICAgbGVmdDogMDtcclxuICAgIHJpZ2h0OiAwO1xyXG4gICAgY29udGVudDogJyc7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICBib3R0b206IDA7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgcmlnaHQ6IDA7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMGRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAxKSwgdHJhbnNwYXJlbnQpO1xyXG4gICAgaGVpZ2h0OiA1MCU7XHJcbiAgfVxyXG4gIC5jYXJkIHtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWJzLWJvcmRlci1jb2xvcik7XHJcbiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICB9XHJcbn1cclxuLmNob29zZS1zZWN0aW9uIHtcclxuICBwYWRkaW5nLWJvdHRvbTogMDtcclxuICBwYWRkaW5nLXRvcDogMzBweDtcclxuICAuaGFuZC1pbWcge1xyXG4gICAgd2lkdGg6IDIyMHB4O1xyXG4gICAgZmlsdGVyOiBkcm9wLXNoYWRvdyhyZ2JhKDMzLCAxNTAsIDI0MywgMC4zKSAwcHggMHB4IDUwcHgpO1xyXG4gIH1cclxuICBoMiB7XHJcbiAgICBmb250LXNpemU6IDIuNXJlbTtcclxuICB9XHJcbn1cclxuLmNob29zZS1zbGlkZXIge1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICB3aWR0aDogMTAwJTtcclxuICBoZWlnaHQ6IDIwMHB4O1xyXG4gIC5zd2lwZXItc2xpZGUge1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgZm9udC1zaXplOiAxOHB4O1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBoZWlnaHQ6IDUwcHggIWltcG9ydGFudDtcclxuICAgIG9wYWNpdHk6IDAuMDg7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlLWluLW91dDtcclxuICAgIGgyIHtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICAgICAgY29sb3I6ICNmZmY7XHJcbiAgICB9XHJcbiAgICAmLnN3aXBlci1zbGlkZS1wcmV2LFxyXG4gICAgJi5zd2lwZXItc2xpZGUtbmV4dCB7XHJcbiAgICAgIG9wYWNpdHk6IDAuMjtcclxuICAgIH1cclxuICAgICYuc3dpcGVyLXNsaWRlLWFjdGl2ZSB7XHJcbiAgICAgIG9wYWNpdHk6IDE7XHJcbiAgICAgIGgyIHtcclxuICAgICAgICBjb2xvcjogdmFyKC0tYnMtcHJpbWFyeSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuLmZyYW1ld29ya3MtY2FyZCB7XHJcbiAgYmFja2dyb3VuZDogdmFyKC0tYnMtYm9keS1iZyk7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBtYXJnaW46IDEwcHggMCAzNXB4O1xyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZDogcmdiYSh2YXIoLS1icy1wcmltYXJ5LXJnYiksIDAuMDgpO1xyXG4gICAgYm94LXNoYWRvdzogMCA0cHggMTVweCAwIHJnYmEodmFyKC0tYnMtcHJpbWFyeS1yZ2IpLCAwLjE1KTtcclxuICB9XHJcbiAgLmZyYW1ld29ya3MtbG9nbyB7XHJcbiAgICB3aWR0aDogYXV0bztcclxuICAgIGhlaWdodDogNDhweDtcclxuICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XHJcbiAgfVxyXG4gIC5mcmFtZXdvcmtzLWJhZGdlIHtcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHRvcDogMTAwJTtcclxuICAgIGxlZnQ6IDUwJTtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpO1xyXG4gICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgIGJhY2tncm91bmQ6IHZhcigtLWJzLWNhcmQtYmcpO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tYnMtcHJpbWFyeSk7XHJcbiAgICBjb2xvcjogdmFyKC0tYnMtcHJpbWFyeSk7XHJcbiAgICBib3JkZXItcmFkaXVzOiAzMHB4O1xyXG4gICAgcGFkZGluZzogMCA1cHg7XHJcbiAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xyXG4gIH1cclxufVxyXG4uZnJhbWV3b3Jrcy1zZWN0aW9uIHtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIC5mcmFtZXdvcmtzLXNsaWRlci5zd2lwZXIge1xyXG4gICAgb3ZlcmZsb3c6IHZpc2libGU7XHJcbiAgfVxyXG59XHJcblxyXG4uZm9vdGVyIHtcclxuICBwYWRkaW5nOiAxMDBweCAwIDA7XHJcbiAgLnN1Yi1mb290ZXIge1xyXG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcclxuICAgIHBhZGRpbmc6IDIwcHggMHB4O1xyXG4gICAgbWFyZ2luLXRvcDogMTAwcHg7XHJcbiAgfVxyXG4gIC5mb290ZXItbGluayB7XHJcbiAgICBhIHtcclxuICAgICAgZGlzcGxheTogaW5saW5lLWZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjFzIGVhc2UtaW4tb3V0O1xyXG4gICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xyXG4gICAgICBtYXJnaW4tdG9wOiAyMHB4O1xyXG4gICAgICBpIHtcclxuICAgICAgICBmb250LXNpemU6IDIwcHg7XHJcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7XHJcbiAgICAgIH1cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuQGtleWZyYW1lcyBzbGlkZVkge1xyXG4gIDAlLFxyXG4gIDUwJSxcclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcclxuICB9XHJcbiAgMjUlIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7XHJcbiAgfVxyXG4gIDc1JSB7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7XHJcbiAgfVxyXG59XHJcbkBrZXlmcmFtZXMgdXBkb3duIHtcclxuICA1MCUge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00MHB4KTtcclxuICB9XHJcbiAgMTAwJSB7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XHJcbiAgfVxyXG59XHJcbkBtZWRpYSAobWF4LXdpZHRoOiA5OTEuOThweCkge1xyXG4gIGhlYWRlciB7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgfVxyXG4gIHNlY3Rpb24ge1xyXG4gICAgcGFkZGluZzogNDBweCAwO1xyXG4gIH1cclxuICAuY2hvb3NlLXNlY3Rpb24gaDIge1xyXG4gICAgZm9udC1zaXplOiAxLjVyZW07XHJcbiAgfVxyXG59XHJcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjcuOThweCkge1xyXG4gIGhlYWRlciB7XHJcbiAgICBwYWRkaW5nOiAxMDBweCAwIDUwcHg7XHJcbiAgICBoMSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMzBweDtcclxuICAgIH1cclxuICB9XHJcbiAgLmFwcC1zbGlkZXIge1xyXG4gICAgLmF2dGFyIHtcclxuICAgICAgZGlzcGxheTogbm9uZTtcclxuICAgIH1cclxuICB9XHJcbiAgLmNob29zZS1zZWN0aW9uIHtcclxuICAgIHBhZGRpbmctdG9wOiAzMHB4O1xyXG4gICAgcGFkZGluZy1ib3R0b206IDMwcHg7XHJcbiAgfVxyXG4gIC5mb290ZXIgLmZvb3Rlci1saW5rIGEge1xyXG4gICAgbWFyZ2luLXRvcDogOHB4O1xyXG4gIH1cclxufVxyXG5AbWVkaWEgKG1heC13aWR0aDogNTc1Ljk4cHgpIHtcclxuICAuY2hvb3NlLXNlY3Rpb24gaDIge1xyXG4gICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgfVxyXG4gIC5jaG9vc2Utc2xpZGVyIHtcclxuICAgIGhlaWdodDogMTIwcHg7XHJcbiAgICAuc3dpcGVyLXNsaWRlIHtcclxuICAgICAgaGVpZ2h0OiAzMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbltkYXRhLXBjLWRpcmVjdGlvbj0ncnRsJ10ge1xyXG4gIGhlYWRlciAuaGVyby1pbWFnZSB7XHJcbiAgICB0cmFuc2Zvcm0tb3JpZ2luOiB0b3AgcmlnaHQ7XHJcbiAgfVxyXG59XHJcbltkYXRhLXBjLXRoZW1lPSdkYXJrJ10ge1xyXG4gIGhlYWRlciB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2IoMTcsIDI1LCA1NCk7XHJcbiAgfVxyXG4gIEBlYWNoICRuYW1lLCAkdmFsdWUgaW4gJHByZXNldC1jb2xvcnMge1xyXG4gICAgJltkYXRhLXBjLXByZXNldD0nI3skbmFtZX0nXSB7XHJcbiAgICAgICRwYy1wcmltYXJ5OiBtYXAtZ2V0KCR2YWx1ZSwgJ3ByaW1hcnknKTtcclxuICAgICAgJHBjLXNlY29uZGFyeTogbWFwLWdldCgkdmFsdWUsICdzZWNvbmRhcnknKTtcclxuICAgICAgJHBjdGhlbWUtY29sb3JzOiAoXHJcbiAgICAgICAgJ3ByaW1hcnknOiAkcGMtcHJpbWFyeSxcclxuICAgICAgICAnc2Vjb25kYXJ5JzogJHBjLXNlY29uZGFyeVxyXG4gICAgICApO1xyXG4gICAgICAkcGMtYm9keTogbWFwLWdldCgkZGFyay1wcmVzZXQtY29sb3JzLCAkbmFtZSwgJ2JvZHknKTtcclxuICAgICAgLmFwcC1zbGlkZXIgLmF2dGFyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAkcGMtYm9keTtcclxuICAgICAgfVxyXG4gICAgICAubmF2YmFyLm5hdmJhci1saWdodCB7XHJcbiAgICAgICAgLm5hdmJhci10b2dnbGVyLWljb24ge1xyXG4gICAgICAgICAgZmlsdGVyOiBpbnZlcnQoMSkgZ3JheXNjYWxlKDEwMCUpIGJyaWdodG5lc3MoMjAwJSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgICY6bm90KC5jYXJkKSB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudGl6ZSgkcGMtYm9keSwgMC4yKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuIiwiLy8gVmFyaWFibGVzXHJcbi8vXHJcbi8vIFZhcmlhYmxlcyBzaG91bGQgZm9sbG93IHRoZSBgJGNvbXBvbmVudC1zdGF0ZS1wcm9wZXJ0eS1zaXplYCBmb3JtdWxhIGZvclxyXG4vLyBjb25zaXN0ZW50IG5hbWluZy4gRXg6ICRuYXYtbGluay1kaXNhYmxlZC1jb2xvciBhbmQgJG1vZGFsLWNvbnRlbnQtYm94LXNoYWRvdy14cy5cclxuXHJcbi8vIENvbG9yIHN5c3RlbVxyXG5cclxuLy8gZnVzdi1kaXNhYmxlXHJcbiRncmF5czogKFxyXG4gICcxMDAnOiAkZ3JheS0xMDAsXHJcbiAgJzIwMCc6ICRncmF5LTIwMCxcclxuICAnMzAwJzogJGdyYXktMzAwLFxyXG4gICc0MDAnOiAkZ3JheS00MDAsXHJcbiAgJzUwMCc6ICRncmF5LTUwMCxcclxuICAnNjAwJzogJGdyYXktNjAwLFxyXG4gICc3MDAnOiAkZ3JheS03MDAsXHJcbiAgJzgwMCc6ICRncmF5LTgwMCxcclxuICAnOTAwJzogJGdyYXktOTAwXHJcbik7XHJcbi8vIGZ1c3YtZW5hYmxlXHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgY29sb3JzLW1hcFxyXG4kY29sb3JzOiAoXHJcbiAgJ2JsdWUnOiAkYmx1ZSxcclxuICAnaW5kaWdvJzogJGluZGlnbyxcclxuICAncHVycGxlJzogJHB1cnBsZSxcclxuICAncGluayc6ICRwaW5rLFxyXG4gICdyZWQnOiAkcmVkLFxyXG4gICdvcmFuZ2UnOiAkb3JhbmdlLFxyXG4gICd5ZWxsb3cnOiAkeWVsbG93LFxyXG4gICdncmVlbic6ICRncmVlbixcclxuICAndGVhbCc6ICR0ZWFsLFxyXG4gICdjeWFuJzogJGN5YW4sXHJcbiAgJ2JsYWNrJzogJGJsYWNrLFxyXG4gICd3aGl0ZSc6ICR3aGl0ZSxcclxuICAnZ3JheSc6ICRncmF5LTYwMCxcclxuICAnZ3JheS1kYXJrJzogJGdyYXktODAwXHJcbik7XHJcbi8vIHNjc3MtZG9jcy1lbmQgY29sb3JzLW1hcFxyXG5cclxuJHByaW1hcnk6ICRibHVlO1xyXG4kc2Vjb25kYXJ5OiAkaW5kaWdvO1xyXG4kc3VjY2VzczogJGdyZWVuO1xyXG4kaW5mbzogJGN5YW47XHJcbiR3YXJuaW5nOiAkeWVsbG93O1xyXG4kZGFuZ2VyOiAkcmVkO1xyXG4kbGlnaHQ6ICRncmF5LTEwMDtcclxuJGRhcms6ICRkYXJrO1xyXG5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IHRoZW1lLWNvbG9ycy1tYXBcclxuJHRoZW1lLWNvbG9yczogKFxyXG4gICdwcmltYXJ5JzogJHByaW1hcnksXHJcbiAgJ3NlY29uZGFyeSc6ICRzZWNvbmRhcnksXHJcbiAgJ3N1Y2Nlc3MnOiAkc3VjY2VzcyxcclxuICAnaW5mbyc6ICRpbmZvLFxyXG4gICd3YXJuaW5nJzogJHdhcm5pbmcsXHJcbiAgJ2Rhbmdlcic6ICRkYW5nZXIsXHJcbiAgJ2xpZ2h0JzogJGxpZ2h0LFxyXG4gICdkYXJrJzogJGRhcmtcclxuKTtcclxuLy8gc2Nzcy1kb2NzLWVuZCB0aGVtZS1jb2xvcnMtbWFwXHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgdGhlbWUtY29sb3JzLXJnYlxyXG4kdGhlbWUtY29sb3JzLXJnYjogbWFwLWxvb3AoJHRoZW1lLWNvbG9ycywgdG8tcmdiLCAnJHZhbHVlJyk7XHJcbi8vIHNjc3MtZG9jcy1lbmQgdGhlbWUtY29sb3JzLXJnYlxyXG5cclxuLy8gVGhlIGNvbnRyYXN0IHJhdGlvIHRvIHJlYWNoIGFnYWluc3Qgd2hpdGUsIHRvIGRldGVybWluZSBpZiBjb2xvciBjaGFuZ2VzIGZyb20gXCJsaWdodFwiIHRvIFwiZGFya1wiLiBBY2NlcHRhYmxlIHZhbHVlcyBmb3IgV0NBRyAyLjAgYXJlIDMsIDQuNSBhbmQgNy5cclxuLy8gU2VlIGh0dHBzOi8vd3d3LnczLm9yZy9UUi9XQ0FHMjAvI3Zpc3VhbC1hdWRpby1jb250cmFzdC1jb250cmFzdFxyXG4kbWluLWNvbnRyYXN0LXJhdGlvOiAxLjU1O1xyXG5cclxuLy8gQ3VzdG9taXplIHRoZSBsaWdodCBhbmQgZGFyayB0ZXh0IGNvbG9ycyBmb3IgdXNlIGluIG91ciBjb2xvciBjb250cmFzdCBmdW5jdGlvbi5cclxuJGNvbG9yLWNvbnRyYXN0LWRhcms6ICRib2R5LWNvbG9yO1xyXG4kY29sb3ItY29udHJhc3QtbGlnaHQ6ICR3aGl0ZTtcclxuXHJcbiRibHVlczogKFxyXG4gICdibHVlLTEwMCc6ICRibHVlLTEwMCxcclxuICAnYmx1ZS0yMDAnOiAkYmx1ZS0yMDAsXHJcbiAgJ2JsdWUtMzAwJzogJGJsdWUtMzAwLFxyXG4gICdibHVlLTQwMCc6ICRibHVlLTQwMCxcclxuICAnYmx1ZS01MDAnOiAkYmx1ZS01MDAsXHJcbiAgJ2JsdWUtNjAwJzogJGJsdWUtNjAwLFxyXG4gICdibHVlLTcwMCc6ICRibHVlLTcwMCxcclxuICAnYmx1ZS04MDAnOiAkYmx1ZS04MDAsXHJcbiAgJ2JsdWUtOTAwJzogJGJsdWUtOTAwXHJcbik7XHJcblxyXG4kaW5kaWdvczogKFxyXG4gICdpbmRpZ28tMTAwJzogJGluZGlnby0xMDAsXHJcbiAgJ2luZGlnby0yMDAnOiAkaW5kaWdvLTIwMCxcclxuICAnaW5kaWdvLTMwMCc6ICRpbmRpZ28tMzAwLFxyXG4gICdpbmRpZ28tNDAwJzogJGluZGlnby00MDAsXHJcbiAgJ2luZGlnby01MDAnOiAkaW5kaWdvLTUwMCxcclxuICAnaW5kaWdvLTYwMCc6ICRpbmRpZ28tNjAwLFxyXG4gICdpbmRpZ28tNzAwJzogJGluZGlnby03MDAsXHJcbiAgJ2luZGlnby04MDAnOiAkaW5kaWdvLTgwMCxcclxuICAnaW5kaWdvLTkwMCc6ICRpbmRpZ28tOTAwXHJcbik7XHJcblxyXG4kcHVycGxlczogKFxyXG4gICdwdXJwbGUtMTAwJzogJHB1cnBsZS0xMDAsXHJcbiAgJ3B1cnBsZS0yMDAnOiAkcHVycGxlLTIwMCxcclxuICAncHVycGxlLTMwMCc6ICRwdXJwbGUtMzAwLFxyXG4gICdwdXJwbGUtNDAwJzogJHB1cnBsZS00MDAsXHJcbiAgJ3B1cnBsZS01MDAnOiAkcHVycGxlLTUwMCxcclxuICAncHVycGxlLTYwMCc6ICRwdXJwbGUtNjAwLFxyXG4gICdwdXJwbGUtNzAwJzogJHB1cnBsZS03MDAsXHJcbiAgJ3B1cnBsZS04MDAnOiAkcHVycGxlLTgwMCxcclxuICAncHVycGxlLTkwMCc6ICRwdXJwbGUtOTAwXHJcbik7XHJcblxyXG4kcGlua3M6IChcclxuICAncGluay0xMDAnOiAkcGluay0xMDAsXHJcbiAgJ3BpbmstMjAwJzogJHBpbmstMjAwLFxyXG4gICdwaW5rLTMwMCc6ICRwaW5rLTMwMCxcclxuICAncGluay00MDAnOiAkcGluay00MDAsXHJcbiAgJ3BpbmstNTAwJzogJHBpbmstNTAwLFxyXG4gICdwaW5rLTYwMCc6ICRwaW5rLTYwMCxcclxuICAncGluay03MDAnOiAkcGluay03MDAsXHJcbiAgJ3BpbmstODAwJzogJHBpbmstODAwLFxyXG4gICdwaW5rLTkwMCc6ICRwaW5rLTkwMFxyXG4pO1xyXG5cclxuJHJlZHM6IChcclxuICAncmVkLTEwMCc6ICRyZWQtMTAwLFxyXG4gICdyZWQtMjAwJzogJHJlZC0yMDAsXHJcbiAgJ3JlZC0zMDAnOiAkcmVkLTMwMCxcclxuICAncmVkLTQwMCc6ICRyZWQtNDAwLFxyXG4gICdyZWQtNTAwJzogJHJlZC01MDAsXHJcbiAgJ3JlZC02MDAnOiAkcmVkLTYwMCxcclxuICAncmVkLTcwMCc6ICRyZWQtNzAwLFxyXG4gICdyZWQtODAwJzogJHJlZC04MDAsXHJcbiAgJ3JlZC05MDAnOiAkcmVkLTkwMFxyXG4pO1xyXG5cclxuJG9yYW5nZXM6IChcclxuICAnb3JhbmdlLTEwMCc6ICRvcmFuZ2UtMTAwLFxyXG4gICdvcmFuZ2UtMjAwJzogJG9yYW5nZS0yMDAsXHJcbiAgJ29yYW5nZS0zMDAnOiAkb3JhbmdlLTMwMCxcclxuICAnb3JhbmdlLTQwMCc6ICRvcmFuZ2UtNDAwLFxyXG4gICdvcmFuZ2UtNTAwJzogJG9yYW5nZS01MDAsXHJcbiAgJ29yYW5nZS02MDAnOiAkb3JhbmdlLTYwMCxcclxuICAnb3JhbmdlLTcwMCc6ICRvcmFuZ2UtNzAwLFxyXG4gICdvcmFuZ2UtODAwJzogJG9yYW5nZS04MDAsXHJcbiAgJ29yYW5nZS05MDAnOiAkb3JhbmdlLTkwMFxyXG4pO1xyXG5cclxuJHllbGxvd3M6IChcclxuICAneWVsbG93LTEwMCc6ICR5ZWxsb3ctMTAwLFxyXG4gICd5ZWxsb3ctMjAwJzogJHllbGxvdy0yMDAsXHJcbiAgJ3llbGxvdy0zMDAnOiAkeWVsbG93LTMwMCxcclxuICAneWVsbG93LTQwMCc6ICR5ZWxsb3ctNDAwLFxyXG4gICd5ZWxsb3ctNTAwJzogJHllbGxvdy01MDAsXHJcbiAgJ3llbGxvdy02MDAnOiAkeWVsbG93LTYwMCxcclxuICAneWVsbG93LTcwMCc6ICR5ZWxsb3ctNzAwLFxyXG4gICd5ZWxsb3ctODAwJzogJHllbGxvdy04MDAsXHJcbiAgJ3llbGxvdy05MDAnOiAkeWVsbG93LTkwMFxyXG4pO1xyXG5cclxuJGdyZWVuczogKFxyXG4gICdncmVlbi0xMDAnOiAkZ3JlZW4tMTAwLFxyXG4gICdncmVlbi0yMDAnOiAkZ3JlZW4tMjAwLFxyXG4gICdncmVlbi0zMDAnOiAkZ3JlZW4tMzAwLFxyXG4gICdncmVlbi00MDAnOiAkZ3JlZW4tNDAwLFxyXG4gICdncmVlbi01MDAnOiAkZ3JlZW4tNTAwLFxyXG4gICdncmVlbi02MDAnOiAkZ3JlZW4tNjAwLFxyXG4gICdncmVlbi03MDAnOiAkZ3JlZW4tNzAwLFxyXG4gICdncmVlbi04MDAnOiAkZ3JlZW4tODAwLFxyXG4gICdncmVlbi05MDAnOiAkZ3JlZW4tOTAwXHJcbik7XHJcblxyXG4kdGVhbHM6IChcclxuICAndGVhbC0xMDAnOiAkdGVhbC0xMDAsXHJcbiAgJ3RlYWwtMjAwJzogJHRlYWwtMjAwLFxyXG4gICd0ZWFsLTMwMCc6ICR0ZWFsLTMwMCxcclxuICAndGVhbC00MDAnOiAkdGVhbC00MDAsXHJcbiAgJ3RlYWwtNTAwJzogJHRlYWwtNTAwLFxyXG4gICd0ZWFsLTYwMCc6ICR0ZWFsLTYwMCxcclxuICAndGVhbC03MDAnOiAkdGVhbC03MDAsXHJcbiAgJ3RlYWwtODAwJzogJHRlYWwtODAwLFxyXG4gICd0ZWFsLTkwMCc6ICR0ZWFsLTkwMFxyXG4pO1xyXG5cclxuJGN5YW5zOiAoXHJcbiAgJ2N5YW4tMTAwJzogJGN5YW4tMTAwLFxyXG4gICdjeWFuLTIwMCc6ICRjeWFuLTIwMCxcclxuICAnY3lhbi0zMDAnOiAkY3lhbi0zMDAsXHJcbiAgJ2N5YW4tNDAwJzogJGN5YW4tNDAwLFxyXG4gICdjeWFuLTUwMCc6ICRjeWFuLTUwMCxcclxuICAnY3lhbi02MDAnOiAkY3lhbi02MDAsXHJcbiAgJ2N5YW4tNzAwJzogJGN5YW4tNzAwLFxyXG4gICdjeWFuLTgwMCc6ICRjeWFuLTgwMCxcclxuICAnY3lhbi05MDAnOiAkY3lhbi05MDBcclxuKTtcclxuXHJcbiRncmF5czogKFxyXG4gICcxMDAnOiAkZ3JheS0xMDAsXHJcbiAgJzIwMCc6ICRncmF5LTIwMCxcclxuICAnMzAwJzogJGdyYXktMzAwLFxyXG4gICc0MDAnOiAkZ3JheS00MDAsXHJcbiAgJzUwMCc6ICRncmF5LTUwMCxcclxuICAnNjAwJzogJGdyYXktNjAwLFxyXG4gICc3MDAnOiAkZ3JheS03MDAsXHJcbiAgJzgwMCc6ICRncmF5LTgwMCxcclxuICAnOTAwJzogJGdyYXktOTAwXHJcbik7XHJcbi8vIGZ1c3YtZW5hYmxlXHJcblxyXG4vLyBDaGFyYWN0ZXJzIHdoaWNoIGFyZSBlc2NhcGVkIGJ5IHRoZSBlc2NhcGUtc3ZnIGZ1bmN0aW9uXHJcbiRlc2NhcGVkLWNoYXJhY3RlcnM6ICgoJzwnLCAnJTNjJyksICgnPicsICclM2UnKSwgKCcjJywgJyUyMycpLCAoJygnLCAnJTI4JyksICgnKScsICclMjknKSk7XHJcblxyXG4vLyBPcHRpb25zXHJcbi8vXHJcbi8vIFF1aWNrbHkgbW9kaWZ5IGdsb2JhbCBzdHlsaW5nIGJ5IGVuYWJsaW5nIG9yIGRpc2FibGluZyBvcHRpb25hbCBmZWF0dXJlcy5cclxuXHJcbiRlbmFibGUtY2FyZXQ6IHRydWU7XHJcbiRlbmFibGUtcm91bmRlZDogdHJ1ZTtcclxuJGVuYWJsZS1zaGFkb3dzOiBmYWxzZTtcclxuJGVuYWJsZS1ncmFkaWVudHM6IGZhbHNlO1xyXG4kZW5hYmxlLXRyYW5zaXRpb25zOiB0cnVlO1xyXG4kZW5hYmxlLXJlZHVjZWQtbW90aW9uOiB0cnVlO1xyXG4kZW5hYmxlLXNtb290aC1zY3JvbGw6IHRydWU7XHJcbiRlbmFibGUtZ3JpZC1jbGFzc2VzOiB0cnVlO1xyXG4kZW5hYmxlLWNvbnRhaW5lci1jbGFzc2VzOiB0cnVlO1xyXG4kZW5hYmxlLWNzc2dyaWQ6IGZhbHNlO1xyXG4kZW5hYmxlLWJ1dHRvbi1wb2ludGVyczogdHJ1ZTtcclxuJGVuYWJsZS1yZnM6IHRydWU7XHJcbiRlbmFibGUtdmFsaWRhdGlvbi1pY29uczogdHJ1ZTtcclxuJGVuYWJsZS1uZWdhdGl2ZS1tYXJnaW5zOiB0cnVlO1xyXG4kZW5hYmxlLWRlcHJlY2F0aW9uLW1lc3NhZ2VzOiB0cnVlO1xyXG4kZW5hYmxlLWltcG9ydGFudC11dGlsaXRpZXM6IHRydWU7XHJcblxyXG4vLyBQcmVmaXggZm9yIDpyb290IENTUyB2YXJpYWJsZXNcclxuXHJcbiR2YXJpYWJsZS1wcmVmaXg6IGJzLTsgLy8gRGVwcmVjYXRlZCBpbiB2NS4yLjAgZm9yIHRoZSBzaG9ydGVyIGAkcHJlZml4YFxyXG4kcHJlZml4OiAkdmFyaWFibGUtcHJlZml4O1xyXG5cclxuLy8gR3JhZGllbnRcclxuLy9cclxuLy8gVGhlIGdyYWRpZW50IHdoaWNoIGlzIGFkZGVkIHRvIGNvbXBvbmVudHMgaWYgYCRlbmFibGUtZ3JhZGllbnRzYCBpcyBgdHJ1ZWBcclxuLy8gVGhpcyBncmFkaWVudCBpcyBhbHNvIGFkZGVkIHRvIGVsZW1lbnRzIHdpdGggYC5iZy1ncmFkaWVudGBcclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IHZhcmlhYmxlLWdyYWRpZW50XHJcbiRncmFkaWVudDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgcmdiYSgkd2hpdGUsIDAuMTUpLCByZ2JhKCR3aGl0ZSwgMCkpO1xyXG4vLyBzY3NzLWRvY3MtZW5kIHZhcmlhYmxlLWdyYWRpZW50XHJcblxyXG4vLyBTcGFjaW5nXHJcbi8vXHJcbi8vIENvbnRyb2wgdGhlIGRlZmF1bHQgc3R5bGluZyBvZiBtb3N0IEJvb3RzdHJhcCBlbGVtZW50cyBieSBtb2RpZnlpbmcgdGhlc2VcclxuLy8gdmFyaWFibGVzLiBNb3N0bHkgZm9jdXNlZCBvbiBzcGFjaW5nLlxyXG4vLyBZb3UgY2FuIGFkZCBtb3JlIGVudHJpZXMgdG8gdGhlICRzcGFjZXJzIG1hcCwgc2hvdWxkIHlvdSBuZWVkIG1vcmUgdmFyaWF0aW9uLlxyXG5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IHNwYWNlci12YXJpYWJsZXMtbWFwc1xyXG4kc3BhY2VyOiAxcmVtO1xyXG4kc3BhY2VyczogKFxyXG4gIDA6IDAsXHJcbiAgMTogJHNwYWNlciAqIDAuMjUsXHJcbiAgMjogJHNwYWNlciAqIDAuNSxcclxuICAzOiAkc3BhY2VyLFxyXG4gIDQ6ICRzcGFjZXIgKiAxLjUsXHJcbiAgNTogJHNwYWNlciAqIDNcclxuKTtcclxuLy8gc2Nzcy1kb2NzLWVuZCBzcGFjZXItdmFyaWFibGVzLW1hcHNcclxuXHJcbi8vIFBvc2l0aW9uXHJcbi8vXHJcbi8vIERlZmluZSB0aGUgZWRnZSBwb3NpdGlvbmluZyBhbmNob3JzIG9mIHRoZSBwb3NpdGlvbiB1dGlsaXRpZXMuXHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgcG9zaXRpb24tbWFwXHJcbiRwb3NpdGlvbi12YWx1ZXM6IChcclxuICAwOiAwLFxyXG4gIDUwOiA1MCUsXHJcbiAgMTAwOiAxMDAlXHJcbik7XHJcbi8vIHNjc3MtZG9jcy1lbmQgcG9zaXRpb24tbWFwXHJcblxyXG4vLyBCb2R5XHJcbi8vXHJcbi8vIFNldHRpbmdzIGZvciB0aGUgYDxib2R5PmAgZWxlbWVudC5cclxuYm9keSB7XHJcbiAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiAnc2FsdCc7XHJcbn1cclxuXHJcbiRib2R5LWJnOiAjZWNlZmYxO1xyXG4kYm9keS1jb2xvcjogJGdyYXktOTAwO1xyXG4kYm9keS10ZXh0LWFsaWduOiBudWxsO1xyXG5cclxuLy8gTGlua3NcclxuLy9cclxuLy8gU3R5bGUgYW5jaG9yIGVsZW1lbnRzLlxyXG5cclxuJGxpbmstY29sb3I6ICRwcmltYXJ5O1xyXG4kbGluay1kZWNvcmF0aW9uOiBub25lO1xyXG4kbGluay1zaGFkZS1wZXJjZW50YWdlOiAyMCU7XHJcbiRsaW5rLWhvdmVyLWNvbG9yOiBzaGlmdC1jb2xvcigkbGluay1jb2xvciwgJGxpbmstc2hhZGUtcGVyY2VudGFnZSk7XHJcbiRsaW5rLWhvdmVyLWRlY29yYXRpb246IHVuZGVybGluZTtcclxuXHJcbiRzdHJldGNoZWQtbGluay1wc2V1ZG8tZWxlbWVudDogYWZ0ZXI7XHJcbiRzdHJldGNoZWQtbGluay16LWluZGV4OiAxO1xyXG5cclxuLy8gUGFyYWdyYXBoc1xyXG4vL1xyXG4vLyBTdHlsZSBwIGVsZW1lbnQuXHJcblxyXG4kcGFyYWdyYXBoLW1hcmdpbi1ib3R0b206IDFyZW07XHJcblxyXG4vLyBHcmlkIGJyZWFrcG9pbnRzXHJcbi8vXHJcbi8vIERlZmluZSB0aGUgbWluaW11bSBkaW1lbnNpb25zIGF0IHdoaWNoIHlvdXIgbGF5b3V0IHdpbGwgY2hhbmdlLFxyXG4vLyBhZGFwdGluZyB0byBkaWZmZXJlbnQgc2NyZWVuIHNpemVzLCBmb3IgdXNlIGluIG1lZGlhIHF1ZXJpZXMuXHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgZ3JpZC1icmVha3BvaW50c1xyXG4kZ3JpZC1icmVha3BvaW50czogKFxyXG4gIHhzOiAwLFxyXG4gIHNtOiA1NzZweCxcclxuICBtZDogNzY4cHgsXHJcbiAgbGc6IDk5MnB4LFxyXG4gIHhsOiAxMjAwcHgsXHJcbiAgeHhsOiAxNDAwcHhcclxuKTtcclxuLy8gc2Nzcy1kb2NzLWVuZCBncmlkLWJyZWFrcG9pbnRzXHJcblxyXG5AaW5jbHVkZSBfYXNzZXJ0LWFzY2VuZGluZygkZ3JpZC1icmVha3BvaW50cywgJyRncmlkLWJyZWFrcG9pbnRzJyk7XHJcbkBpbmNsdWRlIF9hc3NlcnQtc3RhcnRzLWF0LXplcm8oJGdyaWQtYnJlYWtwb2ludHMsICckZ3JpZC1icmVha3BvaW50cycpO1xyXG5cclxuLy8gR3JpZCBjb250YWluZXJzXHJcbi8vXHJcbi8vIERlZmluZSB0aGUgbWF4aW11bSB3aWR0aCBvZiBgLmNvbnRhaW5lcmAgZm9yIGRpZmZlcmVudCBzY3JlZW4gc2l6ZXMuXHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgY29udGFpbmVyLW1heC13aWR0aHNcclxuJGNvbnRhaW5lci1tYXgtd2lkdGhzOiAoXHJcbiAgc206IDU0MHB4LFxyXG4gIG1kOiA3MjBweCxcclxuICBsZzogOTYwcHgsXHJcbiAgeGw6IDExNDBweCxcclxuICB4eGw6IDEzMjBweFxyXG4pO1xyXG4vLyBzY3NzLWRvY3MtZW5kIGNvbnRhaW5lci1tYXgtd2lkdGhzXHJcblxyXG5AaW5jbHVkZSBfYXNzZXJ0LWFzY2VuZGluZygkY29udGFpbmVyLW1heC13aWR0aHMsICckY29udGFpbmVyLW1heC13aWR0aHMnKTtcclxuXHJcbi8vIEdyaWQgY29sdW1uc1xyXG4vL1xyXG4vLyBTZXQgdGhlIG51bWJlciBvZiBjb2x1bW5zIGFuZCBzcGVjaWZ5IHRoZSB3aWR0aCBvZiB0aGUgZ3V0dGVycy5cclxuXHJcbiRncmlkLWNvbHVtbnM6IDEyO1xyXG4kZ3JpZC1ndXR0ZXItd2lkdGg6IDEuNXJlbTtcclxuJGdyaWQtcm93LWNvbHVtbnM6IDY7XHJcblxyXG4vLyBDb250YWluZXIgcGFkZGluZ1xyXG5cclxuJGNvbnRhaW5lci1wYWRkaW5nLXg6ICRncmlkLWd1dHRlci13aWR0aDtcclxuXHJcbi8vIENvbXBvbmVudHNcclxuLy9cclxuLy8gRGVmaW5lIGNvbW1vbiBwYWRkaW5nIGFuZCBib3JkZXIgcmFkaXVzIHNpemVzIGFuZCBtb3JlLlxyXG5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IGJvcmRlci12YXJpYWJsZXNcclxuJGJvcmRlci13aWR0aDogMXB4O1xyXG4kYm9yZGVyLXdpZHRoczogKFxyXG4gIDA6IDAsXHJcbiAgMTogMXB4LFxyXG4gIDI6IDJweCxcclxuICAzOiAzcHgsXHJcbiAgNDogNHB4LFxyXG4gIDU6IDVweFxyXG4pO1xyXG5cclxuJGJvcmRlci1zdHlsZTogc29saWQ7XHJcbiRib3JkZXItY29sb3I6ICNlZWVlZWU7XHJcbiRib3JkZXItY29sb3ItdHJhbnNsdWNlbnQ6IHJnYmEoJGJsYWNrLCAwLjE3NSk7XHJcbi8vIHNjc3MtZG9jcy1lbmQgYm9yZGVyLXZhcmlhYmxlc1xyXG5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IGJvcmRlci1yYWRpdXMtdmFyaWFibGVzXHJcbiRib3JkZXItcmFkaXVzOiA4cHg7XHJcbiRib3JkZXItcmFkaXVzLXNtOiA0cHg7XHJcbiRib3JkZXItcmFkaXVzLWxnOiAxMHB4O1xyXG4kYm9yZGVyLXJhZGl1cy14bDogMXJlbTtcclxuJGJvcmRlci1yYWRpdXMtMnhsOiAycmVtO1xyXG4kYm9yZGVyLXJhZGl1cy1waWxsOiA1MHJlbTtcclxuLy8gc2Nzcy1kb2NzLWVuZCBib3JkZXItcmFkaXVzLXZhcmlhYmxlc1xyXG5cclxuJGJveC1zaGFkb3ctc206IDAgMC4xMjVyZW0gMC4yNXJlbSByZ2JhKCRibGFjaywgMC4wNzUpO1xyXG4kYm94LXNoYWRvdzogMCAwLjVyZW0gMXJlbSByZ2JhKCRibGFjaywgMC4xNSk7XHJcbiRib3gtc2hhZG93LWxnOiAwIDFyZW0gM3JlbSByZ2JhKCRibGFjaywgMC4xNzUpO1xyXG4kYm94LXNoYWRvdy1pbnNldDogaW5zZXQgMCAxcHggMnB4IHJnYmEoJGJsYWNrLCAwLjA3NSk7XHJcblxyXG4kY29tcG9uZW50LWFjdGl2ZS1jb2xvcjogJHdoaXRlO1xyXG4kY29tcG9uZW50LWFjdGl2ZS1iZzogdmFyKC0tYnMtcHJpbWFyeSk7XHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgY2FyZXQtdmFyaWFibGVzXHJcbiRjYXJldC13aWR0aDogMC4zZW07XHJcbiRjYXJldC12ZXJ0aWNhbC1hbGlnbjogJGNhcmV0LXdpZHRoICogMC44NTtcclxuJGNhcmV0LXNwYWNpbmc6ICRjYXJldC13aWR0aCAqIDAuODU7XHJcbi8vIHNjc3MtZG9jcy1lbmQgY2FyZXQtdmFyaWFibGVzXHJcblxyXG4kdHJhbnNpdGlvbi1iYXNlOiBhbGwgMC4ycyBlYXNlLWluLW91dDtcclxuJHRyYW5zaXRpb24tZmFkZTogb3BhY2l0eSAwLjE1cyBsaW5lYXI7XHJcbi8vIHNjc3MtZG9jcy1zdGFydCBjb2xsYXBzZS10cmFuc2l0aW9uXHJcbiR0cmFuc2l0aW9uLWNvbGxhcHNlOiBoZWlnaHQgMC4zNXMgZWFzZTtcclxuJHRyYW5zaXRpb24tY29sbGFwc2Utd2lkdGg6IHdpZHRoIDAuMzVzIGVhc2U7XHJcblxyXG4vLyBzdHlsZWxpbnQtZGlzYWJsZSBmdW5jdGlvbi1kaXNhbGxvd2VkLWxpc3RcclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IGFzcGVjdC1yYXRpb3NcclxuJGFzcGVjdC1yYXRpb3M6IChcclxuICAnMXgxJzogMTAwJSxcclxuICAnNHgzJzogY2FsYygzIC8gNCAqIDEwMCUpLFxyXG4gICcxNng5JzogY2FsYyg5IC8gMTYgKiAxMDAlKSxcclxuICAnMjF4OSc6IGNhbGMoOSAvIDIxICogMTAwJSlcclxuKTtcclxuLy8gc2Nzcy1kb2NzLWVuZCBhc3BlY3QtcmF0aW9zXHJcbi8vIHN0eWxlbGludC1lbmFibGUgZnVuY3Rpb24tZGlzYWxsb3dlZC1saXN0XHJcblxyXG4vLyBUeXBvZ3JhcGh5XHJcbi8vXHJcbi8vIEZvbnQsIGxpbmUtaGVpZ2h0LCBhbmQgY29sb3IgZm9yIGJvZHkgdGV4dCwgaGVhZGluZ3MsIGFuZCBtb3JlLlxyXG5cclxuLy8gc3R5bGVsaW50LWRpc2FibGUgdmFsdWUta2V5d29yZC1jYXNlXHJcbiRmb250LWZhbWlseS1zYW5zLXNlcmlmOiAnUm9ib3RvJywgc2Fucy1zZXJpZjtcclxuJGZvbnQtZmFtaWx5LW1vbm9zcGFjZTogU0ZNb25vLVJlZ3VsYXIsIE1lbmxvLCBNb25hY28sIENvbnNvbGFzLCAnTGliZXJhdGlvbiBNb25vJywgJ0NvdXJpZXIgTmV3JywgbW9ub3NwYWNlO1xyXG4vLyBzdHlsZWxpbnQtZW5hYmxlIHZhbHVlLWtleXdvcmQtY2FzZVxyXG4kZm9udC1mYW1pbHktYmFzZTogdmFyKC0tI3skdmFyaWFibGUtcHJlZml4fWZvbnQtc2Fucy1zZXJpZik7XHJcbiRmb250LWZhbWlseS1jb2RlOiB2YXIoLS0jeyR2YXJpYWJsZS1wcmVmaXh9Zm9udC1tb25vc3BhY2UpO1xyXG5cclxuJGZvbnQtc2l6ZS1yb290OiBudWxsO1xyXG4kZm9udC1zaXplLWJhc2U6IDAuODc1cmVtO1xyXG4kZm9udC1zaXplLXNtOiAkZm9udC1zaXplLWJhc2UgKiAwLjg3NTtcclxuJGZvbnQtc2l6ZS1sZzogJGZvbnQtc2l6ZS1iYXNlICogMS4yNTtcclxuJGZvbnQtd2VpZ2h0LWxpZ2h0ZXI6IGxpZ2h0ZXI7XHJcbiRmb250LXdlaWdodC1saWdodDogMzAwO1xyXG4kZm9udC13ZWlnaHQtbm9ybWFsOiA0MDA7XHJcbiRmb250LXdlaWdodC1zZW1pYm9sZDogNjAwO1xyXG4kZm9udC13ZWlnaHQtYm9sZDogNzAwO1xyXG4kZm9udC13ZWlnaHQtYm9sZGVyOiBib2xkZXI7XHJcblxyXG4kZm9udC13ZWlnaHQtYmFzZTogJGZvbnQtd2VpZ2h0LW5vcm1hbDtcclxuJGxpbmUtaGVpZ2h0LWJhc2U6IDEuNTtcclxuJGxpbmUtaGVpZ2h0LXNtOiAxLjI1O1xyXG4kbGluZS1oZWlnaHQtbGc6IDI7XHJcblxyXG4kaDEtZm9udC1zaXplOiAzNHB4O1xyXG4kaDItZm9udC1zaXplOiAyNHB4O1xyXG4kaDMtZm9udC1zaXplOiAyMHB4O1xyXG4kaDQtZm9udC1zaXplOiAxNnB4O1xyXG4kaDUtZm9udC1zaXplOiAxNHB4O1xyXG4kaDYtZm9udC1zaXplOiAxMnB4O1xyXG5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IGZvbnQtc2l6ZXNcclxuJGZvbnQtc2l6ZXM6IChcclxuICAxOiAkaDEtZm9udC1zaXplLFxyXG4gIDI6ICRoMi1mb250LXNpemUsXHJcbiAgMzogJGgzLWZvbnQtc2l6ZSxcclxuICA0OiAkaDQtZm9udC1zaXplLFxyXG4gIDU6ICRoNS1mb250LXNpemUsXHJcbiAgNjogJGg2LWZvbnQtc2l6ZVxyXG4pO1xyXG5cclxuLy8gc2Nzcy1kb2NzLWVuZCBmb250LXNpemVzXHJcblxyXG4kaGVhZGluZ3MtbWFyZ2luLWJvdHRvbTogY2FsYygkc3BhY2VyIC8gMik7XHJcbiRoZWFkaW5ncy1mb250LWZhbWlseTogbnVsbDtcclxuJGhlYWRpbmdzLWZvbnQtc3R5bGU6IG51bGw7XHJcbiRoZWFkaW5ncy1mb250LXdlaWdodDogNTAwO1xyXG4kaGVhZGluZ3MtbGluZS1oZWlnaHQ6IDEuMjtcclxuJGhlYWRpbmdzLWNvbG9yOiAkZ3JheS05MDA7XHJcbiRsYWJlbC1jb2xvcjogJGdyYXktOTAwO1xyXG5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IGRpc3BsYXktaGVhZGluZ3NcclxuJGRpc3BsYXktZm9udC1zaXplczogKFxyXG4gIDE6IDVyZW0sXHJcbiAgMjogNC41cmVtLFxyXG4gIDM6IDRyZW0sXHJcbiAgNDogMy41cmVtLFxyXG4gIDU6IDNyZW0sXHJcbiAgNjogMi41cmVtXHJcbik7XHJcblxyXG4kZGlzcGxheS1mb250LXdlaWdodDogMzAwO1xyXG4kZGlzcGxheS1saW5lLWhlaWdodDogJGhlYWRpbmdzLWxpbmUtaGVpZ2h0O1xyXG4vLyBzY3NzLWRvY3MtZW5kIGRpc3BsYXktaGVhZGluZ3NcclxuXHJcbiRsZWFkLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1iYXNlICogMS4yNTtcclxuJGxlYWQtZm9udC13ZWlnaHQ6IDMwMDtcclxuXHJcbiRzbWFsbC1mb250LXNpemU6IDgwJTtcclxuXHJcbiRzdWItc3VwLWZvbnQtc2l6ZTogMC43NWVtO1xyXG5cclxuJHRleHQtbXV0ZWQ6ICRncmF5LTYwMDtcclxuXHJcbiRpbml0aWFsaXNtLWZvbnQtc2l6ZTogJHNtYWxsLWZvbnQtc2l6ZTtcclxuJGJsb2NrcXVvdGUtbWFyZ2luLXk6ICRzcGFjZXI7XHJcbiRibG9ja3F1b3RlLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1iYXNlICogMS4yNTtcclxuJGJsb2NrcXVvdGUtZm9vdGVyLWNvbG9yOiAkZ3JheS02MDA7XHJcbiRibG9ja3F1b3RlLWZvb3Rlci1mb250LXNpemU6ICRzbWFsbC1mb250LXNpemU7XHJcblxyXG4kaHItbWFyZ2luLXk6ICRzcGFjZXI7XHJcbiRoci1jb2xvcjogaW5oZXJpdDtcclxuXHJcbi8vIGZ1c3YtZGlzYWJsZVxyXG4kaHItYmctY29sb3I6IG51bGw7IC8vIERlcHJlY2F0ZWQgaW4gdjUuMi4wXHJcbiRoci1oZWlnaHQ6IG51bGw7IC8vIERlcHJlY2F0ZWQgaW4gdjUuMi4wXHJcbi8vIGZ1c3YtZW5hYmxlXHJcblxyXG4kaHItYm9yZGVyLWNvbG9yOiBudWxsOyAvLyBBbGxvd3MgZm9yIGluaGVyaXRlZCBjb2xvcnNcclxuJGhyLWJvcmRlci13aWR0aDogJGJvcmRlci13aWR0aDtcclxuJGhyLW9wYWNpdHk6IDAuMTM7XHJcblxyXG4kbGVnZW5kLW1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxuJGxlZ2VuZC1mb250LXNpemU6IDEuNXJlbTtcclxuJGxlZ2VuZC1mb250LXdlaWdodDogbnVsbDtcclxuXHJcbiRtYXJrLXBhZGRpbmc6IDAuMmVtO1xyXG5cclxuJGR0LWZvbnQtd2VpZ2h0OiAkZm9udC13ZWlnaHQtYm9sZDtcclxuXHJcbiRuZXN0ZWQta2JkLWZvbnQtd2VpZ2h0OiAkZm9udC13ZWlnaHQtYm9sZDtcclxuXHJcbiRsaXN0LWlubGluZS1wYWRkaW5nOiAwLjVyZW07XHJcblxyXG4kbWFyay1iZzogI2ZjZjhlMztcclxuLy8gVGFibGVzXHJcbi8vXHJcbi8vIEN1c3RvbWl6ZXMgdGhlIGAudGFibGVgIGNvbXBvbmVudCB3aXRoIGJhc2ljIHZhbHVlcywgZWFjaCB1c2VkIGFjcm9zcyBhbGwgdGFibGUgdmFyaWF0aW9ucy5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IHRhYmxlLXZhcmlhYmxlc1xyXG4kdGFibGUtY2VsbC1wYWRkaW5nLXk6IDAuOXJlbTtcclxuJHRhYmxlLWNlbGwtcGFkZGluZy14OiAwLjc1cmVtO1xyXG4kdGFibGUtY2VsbC1wYWRkaW5nLXktc206IDAuM3JlbTtcclxuJHRhYmxlLWNlbGwtcGFkZGluZy14LXNtOiAwLjNyZW07XHJcblxyXG4kdGFibGUtY2VsbC12ZXJ0aWNhbC1hbGlnbjogdG9wO1xyXG5cclxuJHRhYmxlLWNvbG9yOiAkYm9keS1jb2xvcjtcclxuJHRhYmxlLWJnOiB0cmFuc3BhcmVudDtcclxuJHRhYmxlLWFjY2VudC1iZzogdHJhbnNwYXJlbnQ7XHJcblxyXG4kdGFibGUtdGgtZm9udC13ZWlnaHQ6IG51bGw7XHJcbiR0YWJsZS1zdHJpcGVkLWNvbG9yOiAkdGFibGUtY29sb3I7XHJcbiR0YWJsZS1zdHJpcGVkLWJnLWZhY3RvcjogMC4wNTtcclxuJHRhYmxlLXN0cmlwZWQtYmc6IHJnYmEoJGJsYWNrLCAkdGFibGUtc3RyaXBlZC1iZy1mYWN0b3IpO1xyXG5cclxuJHRhYmxlLWFjdGl2ZS1jb2xvcjogJHRhYmxlLWNvbG9yO1xyXG4kdGFibGUtYWN0aXZlLWJnLWZhY3RvcjogMC4xO1xyXG4kdGFibGUtYWN0aXZlLWJnOiByZ2JhKCRibGFjaywgJHRhYmxlLWFjdGl2ZS1iZy1mYWN0b3IpO1xyXG5cclxuJHRhYmxlLWhvdmVyLWNvbG9yOiAkdGFibGUtY29sb3I7XHJcbiR0YWJsZS1ob3Zlci1iZy1mYWN0b3I6IDAuMDI7XHJcbiR0YWJsZS1ob3Zlci1iZzogcmdiYSgkcHJpbWFyeSwgJHRhYmxlLWhvdmVyLWJnLWZhY3Rvcik7XHJcblxyXG4kdGFibGUtYm9yZGVyLWZhY3RvcjogMC4xO1xyXG4kdGFibGUtYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xyXG4kdGFibGUtYm9yZGVyLWNvbG9yOiAkYm9yZGVyLWNvbG9yO1xyXG5cclxuJHRhYmxlLXN0cmlwZWQtb3JkZXI6IG9kZDtcclxuXHJcbiR0YWJsZS1ncm91cC1zZXBlcmF0b3ItY29sb3I6IGN1cnJlbnRDb2xvcjtcclxuJHRhYmxlLWNhcHRpb24tY29sb3I6ICR0ZXh0LW11dGVkO1xyXG5cclxuJHRhYmxlLWJnLXNjYWxlOiAtODAlO1xyXG5cclxuJHRhYmxlLXZhcmlhbnRzOiAoXHJcbiAgJ3ByaW1hcnknOiBzaGlmdC1jb2xvcigkcHJpbWFyeSwgJHRhYmxlLWJnLXNjYWxlKSxcclxuICAnc2Vjb25kYXJ5Jzogc2hpZnQtY29sb3IoJHNlY29uZGFyeSwgJHRhYmxlLWJnLXNjYWxlKSxcclxuICAnc3VjY2Vzcyc6IHNoaWZ0LWNvbG9yKCRzdWNjZXNzLCAkdGFibGUtYmctc2NhbGUpLFxyXG4gICdpbmZvJzogc2hpZnQtY29sb3IoJGluZm8sICR0YWJsZS1iZy1zY2FsZSksXHJcbiAgJ3dhcm5pbmcnOiBzaGlmdC1jb2xvcigkd2FybmluZywgJHRhYmxlLWJnLXNjYWxlKSxcclxuICAnZGFuZ2VyJzogc2hpZnQtY29sb3IoJGRhbmdlciwgJHRhYmxlLWJnLXNjYWxlKSxcclxuICAnbGlnaHQnOiAkbGlnaHQsXHJcbiAgJ2RhcmsnOiAkZGFya1xyXG4pO1xyXG4vLyBzY3NzLWRvY3MtZW5kIHRhYmxlLXZhcmlhYmxlc1xyXG5cclxuLy8gQnV0dG9ucyArIEZvcm1zXHJcbi8vXHJcbi8vIFNoYXJlZCB2YXJpYWJsZXMgdGhhdCBhcmUgcmVhc3NpZ25lZCB0byBgJGlucHV0LWAgYW5kIGAkYnRuLWAgc3BlY2lmaWMgdmFyaWFibGVzLlxyXG5cclxuJGlucHV0LWJ0bi1wYWRkaW5nLXk6IDAuNXJlbTtcclxuJGlucHV0LWJ0bi1wYWRkaW5nLXg6IDFyZW07XHJcbiRpbnB1dC1idG4tZm9udC1mYW1pbHk6IG51bGw7XHJcbiRpbnB1dC1idG4tZm9udC1zaXplOiAwLjg3NXJlbTtcclxuJGlucHV0LWJ0bi1saW5lLWhlaWdodDogJGxpbmUtaGVpZ2h0LWJhc2U7XHJcblxyXG4kaW5wdXQtYnRuLWZvY3VzLXdpZHRoOiAwLjJyZW07XHJcbiRpbnB1dC1idG4tZm9jdXMtY29sb3Itb3BhY2l0eTogMC4yNTtcclxuJGlucHV0LWJ0bi1mb2N1cy1jb2xvcjogcmdiYSgkY29tcG9uZW50LWFjdGl2ZS1iZywgJGlucHV0LWJ0bi1mb2N1cy1jb2xvci1vcGFjaXR5KTtcclxuJGlucHV0LWJ0bi1mb2N1cy1ibHVyOiAwO1xyXG4kaW5wdXQtYnRuLWZvY3VzLWJveC1zaGFkb3c6IDAgMCAwICRpbnB1dC1idG4tZm9jdXMtd2lkdGggJGlucHV0LWJ0bi1mb2N1cy1jb2xvcjtcclxuXHJcbiRpbnB1dC1idG4tcGFkZGluZy15LXNtOiAwLjI1cmVtO1xyXG4kaW5wdXQtYnRuLXBhZGRpbmcteC1zbTogMC41cmVtO1xyXG4kaW5wdXQtYnRuLWZvbnQtc2l6ZS1zbTogJGZvbnQtc2l6ZS1zbTtcclxuXHJcbiRpbnB1dC1idG4tcGFkZGluZy15LWxnOiAxcmVtO1xyXG4kaW5wdXQtYnRuLXBhZGRpbmcteC1sZzogMS4zcmVtO1xyXG4kaW5wdXQtYnRuLWZvbnQtc2l6ZS1sZzogJGZvbnQtc2l6ZS1sZztcclxuXHJcbiRpbnB1dC1idG4tYm9yZGVyLXdpZHRoOiAxcHg7XHJcblxyXG4vLyBCdXR0b25zXHJcbi8vXHJcbi8vIEZvciBlYWNoIG9mIEJvb3RzdHJhcCdzIGJ1dHRvbnMsIGRlZmluZSB0ZXh0LCBiYWNrZ3JvdW5kLCBhbmQgYm9yZGVyIGNvbG9yLlxyXG5cclxuJGJ0bi1wYWRkaW5nLXk6ICRpbnB1dC1idG4tcGFkZGluZy15O1xyXG4kYnRuLXBhZGRpbmcteDogJGlucHV0LWJ0bi1wYWRkaW5nLXg7XHJcbiRidG4tZm9udC1mYW1pbHk6ICRpbnB1dC1idG4tZm9udC1mYW1pbHk7XHJcbiRidG4tZm9udC1zaXplOiAkaW5wdXQtYnRuLWZvbnQtc2l6ZTtcclxuJGJ0bi1saW5lLWhlaWdodDogJGlucHV0LWJ0bi1saW5lLWhlaWdodDtcclxuJGJ0bi13aGl0ZS1zcGFjZTogbnVsbDsgLy8gU2V0IHRvIGBub3dyYXBgIHRvIHByZXZlbnQgdGV4dCB3cmFwcGluZ1xyXG5cclxuJGJ0bi1wYWRkaW5nLXktc206ICRpbnB1dC1idG4tcGFkZGluZy15LXNtO1xyXG4kYnRuLXBhZGRpbmcteC1zbTogJGlucHV0LWJ0bi1wYWRkaW5nLXgtc207XHJcbiRidG4tZm9udC1zaXplLXNtOiAkaW5wdXQtYnRuLWZvbnQtc2l6ZS1zbTtcclxuXHJcbiRidG4tcGFkZGluZy15LWxnOiAkaW5wdXQtYnRuLXBhZGRpbmcteS1sZztcclxuJGJ0bi1wYWRkaW5nLXgtbGc6ICRpbnB1dC1idG4tcGFkZGluZy14LWxnO1xyXG4kYnRuLWZvbnQtc2l6ZS1sZzogJGlucHV0LWJ0bi1mb250LXNpemUtbGc7XHJcblxyXG4kYnRuLWJvcmRlci13aWR0aDogJGlucHV0LWJ0bi1ib3JkZXItd2lkdGg7XHJcblxyXG4kYnRuLWZvbnQtd2VpZ2h0OiA1MDA7XHJcbiRidG4tYm94LXNoYWRvdzpcclxuICBpbnNldCAwIDFweCAwIHJnYmEoJHdoaXRlLCAwLjE1KSxcclxuICAwIDFweCAxcHggcmdiYSgkYmxhY2ssIDAuMDc1KTtcclxuJGJ0bi1mb2N1cy13aWR0aDogJGlucHV0LWJ0bi1mb2N1cy13aWR0aDtcclxuJGJ0bi1mb2N1cy1ib3gtc2hhZG93OiAkaW5wdXQtYnRuLWZvY3VzLWJveC1zaGFkb3c7XHJcbiRidG4tZGlzYWJsZWQtb3BhY2l0eTogMC42NTtcclxuJGJ0bi1hY3RpdmUtYm94LXNoYWRvdzogaW5zZXQgMCAzcHggNXB4IHJnYmEoJGJsYWNrLCAwLjEyNSk7XHJcblxyXG4kYnRuLWxpbmstY29sb3I6ICRsaW5rLWNvbG9yO1xyXG4kYnRuLWxpbmstaG92ZXItY29sb3I6ICRsaW5rLWhvdmVyLWNvbG9yO1xyXG4kYnRuLWxpbmstZGlzYWJsZWQtY29sb3I6ICRncmF5LTYwMDtcclxuXHJcbi8vIEFsbG93cyBmb3IgY3VzdG9taXppbmcgYnV0dG9uIHJhZGl1cyBpbmRlcGVuZGVudGx5IGZyb20gZ2xvYmFsIGJvcmRlciByYWRpdXNcclxuJGJ0bi1ib3JkZXItcmFkaXVzOiA0cHg7XHJcbiRidG4tYm9yZGVyLXJhZGl1cy1zbTogMnB4O1xyXG4kYnRuLWJvcmRlci1yYWRpdXMtbGc6IDZweDtcclxuXHJcbiRidG4tdHJhbnNpdGlvbjpcclxuICBjb2xvciAwLjE1cyBlYXNlLWluLW91dCxcclxuICBiYWNrZ3JvdW5kLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxyXG4gIGJvcmRlci1jb2xvciAwLjE1cyBlYXNlLWluLW91dCxcclxuICBib3gtc2hhZG93IDAuMTVzIGVhc2UtaW4tb3V0O1xyXG5cclxuJGJ0bi1ob3Zlci1iZy1zaGFkZS1hbW91bnQ6IDE1JTtcclxuJGJ0bi1ob3Zlci1iZy10aW50LWFtb3VudDogMTUlO1xyXG4kYnRuLWhvdmVyLWJvcmRlci1zaGFkZS1hbW91bnQ6IDIwJTtcclxuJGJ0bi1ob3Zlci1ib3JkZXItdGludC1hbW91bnQ6IDEwJTtcclxuJGJ0bi1hY3RpdmUtYmctc2hhZGUtYW1vdW50OiAyMCU7XHJcbiRidG4tYWN0aXZlLWJnLXRpbnQtYW1vdW50OiAyMCU7XHJcbiRidG4tYWN0aXZlLWJvcmRlci1zaGFkZS1hbW91bnQ6IDI1JTtcclxuJGJ0bi1hY3RpdmUtYm9yZGVyLXRpbnQtYW1vdW50OiAxMCU7XHJcbi8vIHNjc3MtZG9jcy1lbmQgYnRuLXZhcmlhYmxlc1xyXG5cclxuLy8gRm9ybXNcclxuXHJcbiRmb3JtLXRleHQtbWFyZ2luLXRvcDogMC4yNXJlbTtcclxuJGZvcm0tdGV4dC1mb250LXNpemU6ICRzbWFsbC1mb250LXNpemU7XHJcbiRmb3JtLXRleHQtZm9udC1zdHlsZTogbnVsbDtcclxuJGZvcm0tdGV4dC1mb250LXdlaWdodDogbnVsbDtcclxuJGZvcm0tdGV4dC1jb2xvcjogJHRleHQtbXV0ZWQ7XHJcblxyXG4kZm9ybS1sYWJlbC1tYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiRmb3JtLWxhYmVsLWZvbnQtc2l6ZTogbnVsbDtcclxuJGZvcm0tbGFiZWwtZm9udC1zdHlsZTogbnVsbDtcclxuJGZvcm0tbGFiZWwtZm9udC13ZWlnaHQ6IG51bGw7XHJcbiRmb3JtLWxhYmVsLWNvbG9yOiAkbGFiZWwtY29sb3I7XHJcblxyXG4kaW5wdXQtcGFkZGluZy15OiAwLjY1cmVtO1xyXG4kaW5wdXQtcGFkZGluZy14OiAwLjc1cmVtO1xyXG4kaW5wdXQtZm9udC1mYW1pbHk6ICRpbnB1dC1idG4tZm9udC1mYW1pbHk7XHJcbiRpbnB1dC1mb250LXNpemU6ICRpbnB1dC1idG4tZm9udC1zaXplO1xyXG4kaW5wdXQtZm9udC13ZWlnaHQ6ICRmb250LXdlaWdodC1iYXNlO1xyXG4kaW5wdXQtbGluZS1oZWlnaHQ6ICRpbnB1dC1idG4tbGluZS1oZWlnaHQ7XHJcblxyXG4kaW5wdXQtcGFkZGluZy15LXNtOiAwLjM3NXJlbTtcclxuJGlucHV0LXBhZGRpbmcteC1zbTogMC43cmVtO1xyXG4kaW5wdXQtZm9udC1zaXplLXNtOiAkaW5wdXQtYnRuLWZvbnQtc2l6ZS1zbTtcclxuXHJcbiRpbnB1dC1wYWRkaW5nLXktbGc6IDAuNzc1cmVtO1xyXG4kaW5wdXQtcGFkZGluZy14LWxnOiAwLjg1cmVtO1xyXG4kaW5wdXQtZm9udC1zaXplLWxnOiAkaW5wdXQtYnRuLWZvbnQtc2l6ZS1sZztcclxuXHJcbiRpbnB1dC1iZzogJHdoaXRlO1xyXG4kaW5wdXQtZGlzYWJsZWQtY29sb3I6IG51bGw7XHJcbiRpbnB1dC1kaXNhYmxlZC1iZzogJGdyYXktMjAwO1xyXG4kaW5wdXQtZGlzYWJsZWQtYm9yZGVyLWNvbG9yOiBudWxsO1xyXG5cclxuJGlucHV0LWNvbG9yOiAkYm9keS1jb2xvcjtcclxuJGlucHV0LWJvcmRlci1jb2xvcjogJGdyYXktNDAwO1xyXG4kaW5wdXQtYm9yZGVyLXdpZHRoOiAxcHg7XHJcbiRpbnB1dC1ib3gtc2hhZG93OiBpbnNldCAwIDFweCAxcHggcmdiYSgkYmxhY2ssIDAuMDc1KTtcclxuXHJcbiRpbnB1dC1ib3JkZXItcmFkaXVzOiA2cHg7XHJcbiRpbnB1dC1ib3JkZXItcmFkaXVzLXNtOiA2cHg7XHJcbiRpbnB1dC1ib3JkZXItcmFkaXVzLWxnOiA2cHg7XHJcblxyXG4kaW5wdXQtZm9jdXMtYmc6ICRpbnB1dC1iZztcclxuJGlucHV0LWZvY3VzLWJvcmRlci1jb2xvcjogJHByaW1hcnk7XHJcbiRpbnB1dC1mb2N1cy1jb2xvcjogJGlucHV0LWNvbG9yO1xyXG4kaW5wdXQtZm9jdXMtd2lkdGg6ICRpbnB1dC1idG4tZm9jdXMtd2lkdGg7XHJcbiRpbnB1dC1mb2N1cy1ib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSgje3ZhcigtLWJzLXByaW1hcnktcmdiKX0sIDAuMik7XHJcblxyXG4kaW5wdXQtcGxhY2Vob2xkZXItY29sb3I6ICRncmF5LTYwMDtcclxuJGlucHV0LXBsYWludGV4dC1jb2xvcjogJGhlYWRpbmdzLWNvbG9yO1xyXG5cclxuJGlucHV0LWhlaWdodC1ib3JkZXI6ICRpbnB1dC1ib3JkZXItd2lkdGggKiAyO1xyXG5cclxuJGlucHV0LWhlaWdodC1pbm5lcjogYWRkKCRpbnB1dC1saW5lLWhlaWdodCAqIDFlbSwgY2FsYygkaW5wdXQtcGFkZGluZy15ICogMikpO1xyXG4kaW5wdXQtaGVpZ2h0LWlubmVyLWhhbGY6IGFkZCgkaW5wdXQtbGluZS1oZWlnaHQgKiAwLjVlbSwgJGlucHV0LXBhZGRpbmcteSk7XHJcbiRpbnB1dC1oZWlnaHQtaW5uZXItcXVhcnRlcjogYWRkKCRpbnB1dC1saW5lLWhlaWdodCAqIDAuMjVlbSwgY2FsYygkaW5wdXQtcGFkZGluZy15IC8gMikpO1xyXG5cclxuJGlucHV0LWhlaWdodDogYWRkKCRpbnB1dC1saW5lLWhlaWdodCAqIDFlbSwgYWRkKCRpbnB1dC1wYWRkaW5nLXkgKiAyLCAkaW5wdXQtaGVpZ2h0LWJvcmRlciwgZmFsc2UpKTtcclxuJGlucHV0LWhlaWdodC1zbTogYWRkKCRpbnB1dC1saW5lLWhlaWdodCAqIDFlbSwgYWRkKCRpbnB1dC1wYWRkaW5nLXktc20gKiAyLCAkaW5wdXQtaGVpZ2h0LWJvcmRlciwgZmFsc2UpKTtcclxuJGlucHV0LWhlaWdodC1sZzogYWRkKCRpbnB1dC1saW5lLWhlaWdodCAqIDFlbSwgYWRkKCRpbnB1dC1wYWRkaW5nLXktbGcgKiAyLCAkaW5wdXQtaGVpZ2h0LWJvcmRlciwgZmFsc2UpKTtcclxuXHJcbiRpbnB1dC10cmFuc2l0aW9uOlxyXG4gIGJvcmRlci1jb2xvciAwLjE1cyBlYXNlLWluLW91dCxcclxuICBib3gtc2hhZG93IDAuMTVzIGVhc2UtaW4tb3V0O1xyXG5cclxuJGZvcm0tY29sb3Itd2lkdGg6IDNyZW07XHJcbi8vIHNjc3MtZG9jcy1lbmQgZm9ybS1pbnB1dC12YXJpYWJsZXNcclxuXHJcbiRmb3JtLWNoZWNrLWlucHV0LXdpZHRoOiAxLjI1ZW07XHJcbiRmb3JtLWNoZWNrLW1pbi1oZWlnaHQ6ICRmb250LXNpemUtYmFzZSAqICRsaW5lLWhlaWdodC1iYXNlO1xyXG4kZm9ybS1jaGVjay1wYWRkaW5nLXN0YXJ0OiAkZm9ybS1jaGVjay1pbnB1dC13aWR0aCArIDAuNWVtO1xyXG4kZm9ybS1jaGVjay1tYXJnaW4tYm90dG9tOiAwLjEyNXJlbTtcclxuJGZvcm0tY2hlY2stbGFiZWwtY29sb3I6IG51bGw7XHJcbiRmb3JtLWNoZWNrLWxhYmVsLWN1cnNvcjogbnVsbDtcclxuJGZvcm0tY2hlY2stdHJhbnNpdGlvbjpcclxuICBiYWNrZ3JvdW5kLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxyXG4gIGJhY2tncm91bmQtcG9zaXRpb24gMC4xNXMgZWFzZS1pbi1vdXQsXHJcbiAgYm9yZGVyLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxyXG4gIGJveC1zaGFkb3cgMC4xNXMgZWFzZS1pbi1vdXQ7XHJcblxyXG4kZm9ybS1jaGVjay1pbnB1dC1hY3RpdmUtZmlsdGVyOiBicmlnaHRuZXNzKDkwJSk7XHJcblxyXG4kZm9ybS1jaGVjay1pbnB1dC1iZzogJGlucHV0LWJnO1xyXG4kZm9ybS1jaGVjay1pbnB1dC1ib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG4kZm9ybS1jaGVjay1pbnB1dC1ib3JkZXItcmFkaXVzOiAwLjI1ZW07XHJcbiRmb3JtLWNoZWNrLXJhZGlvLWJvcmRlci1yYWRpdXM6IDUwJTtcclxuJGZvcm0tY2hlY2staW5wdXQtZm9jdXMtYm9yZGVyOiAkaW5wdXQtZm9jdXMtYm9yZGVyLWNvbG9yO1xyXG4kZm9ybS1jaGVjay1pbnB1dC1mb2N1cy1ib3gtc2hhZG93OiAkaW5wdXQtYnRuLWZvY3VzLWJveC1zaGFkb3c7XHJcblxyXG4kZm9ybS1jaGVjay1pbnB1dC1jaGVja2VkLWNvbG9yOiAkY29tcG9uZW50LWFjdGl2ZS1jb2xvcjtcclxuJGZvcm0tY2hlY2staW5wdXQtY2hlY2tlZC1iZy1jb2xvcjogJGNvbXBvbmVudC1hY3RpdmUtYmc7XHJcbiRmb3JtLWNoZWNrLWlucHV0LWNoZWNrZWQtYm9yZGVyLWNvbG9yOiAkZm9ybS1jaGVjay1pbnB1dC1jaGVja2VkLWJnLWNvbG9yO1xyXG4kZm9ybS1jaGVjay1pbnB1dC1jaGVja2VkLWJnLWltYWdlOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAyMCAyMCc+PHBhdGggZmlsbD0nbm9uZScgc3Ryb2tlPScjeyRmb3JtLWNoZWNrLWlucHV0LWNoZWNrZWQtY29sb3J9JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnIHN0cm9rZS13aWR0aD0nMycgZD0nTTYgMTBsMyAzbDYtNicvPjwvc3ZnPlwiKTtcclxuJGZvcm0tY2hlY2stcmFkaW8tY2hlY2tlZC1iZy1pbWFnZTogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PSctNCAtNCA4IDgnPjxjaXJjbGUgcj0nMicgZmlsbD0nI3skZm9ybS1jaGVjay1pbnB1dC1jaGVja2VkLWNvbG9yfScvPjwvc3ZnPlwiKTtcclxuXHJcbiRmb3JtLWNoZWNrLWlucHV0LWluZGV0ZXJtaW5hdGUtY29sb3I6ICRjb21wb25lbnQtYWN0aXZlLWNvbG9yO1xyXG4kZm9ybS1jaGVjay1pbnB1dC1pbmRldGVybWluYXRlLWJnLWNvbG9yOiAkY29tcG9uZW50LWFjdGl2ZS1iZztcclxuJGZvcm0tY2hlY2staW5wdXQtaW5kZXRlcm1pbmF0ZS1ib3JkZXItY29sb3I6ICRmb3JtLWNoZWNrLWlucHV0LWluZGV0ZXJtaW5hdGUtYmctY29sb3I7XHJcbiRmb3JtLWNoZWNrLWlucHV0LWluZGV0ZXJtaW5hdGUtYmctaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nMCAwIDIwIDIwJz48cGF0aCBmaWxsPSdub25lJyBzdHJva2U9JyN7JGZvcm0tY2hlY2staW5wdXQtaW5kZXRlcm1pbmF0ZS1jb2xvcn0nIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgc3Ryb2tlLXdpZHRoPSczJyBkPSdNNiAxMGg4Jy8+PC9zdmc+XCIpO1xyXG5cclxuJGZvcm0tY2hlY2staW5wdXQtZGlzYWJsZWQtb3BhY2l0eTogMC41O1xyXG4kZm9ybS1jaGVjay1sYWJlbC1kaXNhYmxlZC1vcGFjaXR5OiAkZm9ybS1jaGVjay1pbnB1dC1kaXNhYmxlZC1vcGFjaXR5O1xyXG4kZm9ybS1jaGVjay1idG4tY2hlY2stZGlzYWJsZWQtb3BhY2l0eTogJGJ0bi1kaXNhYmxlZC1vcGFjaXR5O1xyXG4kZm9ybS1jaGVjay1pbmxpbmUtbWFyZ2luLWVuZDogMXJlbTtcclxuJGZvcm0tc3dpdGNoLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG4kZm9ybS1zd2l0Y2gtd2lkdGg6IDJlbTtcclxuJGZvcm0tc3dpdGNoLXBhZGRpbmctc3RhcnQ6ICRmb3JtLXN3aXRjaC13aWR0aCArIDAuNWVtO1xyXG4kZm9ybS1zd2l0Y2gtYmctaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nLTQgLTQgOCA4Jz48Y2lyY2xlIHI9JzMnIGZpbGw9JyN7JGZvcm0tc3dpdGNoLWNvbG9yfScvPjwvc3ZnPlwiKTtcclxuJGZvcm0tc3dpdGNoLWJvcmRlci1yYWRpdXM6ICRmb3JtLXN3aXRjaC13aWR0aDtcclxuXHJcbiRmb3JtLXN3aXRjaC1mb2N1cy1jb2xvcjogJGlucHV0LWZvY3VzLWJvcmRlci1jb2xvcjtcclxuJGZvcm0tc3dpdGNoLWZvY3VzLWJnLWltYWdlOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPScjeyRmb3JtLXN3aXRjaC1mb2N1cy1jb2xvcn0nLz48L3N2Zz5cIik7XHJcblxyXG4kZm9ybS1zd2l0Y2gtY2hlY2tlZC1jb2xvcjogJGNvbXBvbmVudC1hY3RpdmUtY29sb3I7XHJcbiRmb3JtLXN3aXRjaC1jaGVja2VkLWJnLWltYWdlOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPSczJyBmaWxsPScjeyRmb3JtLXN3aXRjaC1jaGVja2VkLWNvbG9yfScvPjwvc3ZnPlwiKTtcclxuJGZvcm0tc3dpdGNoLWNoZWNrZWQtYmctcG9zaXRpb246IHJpZ2h0IGNlbnRlcjtcclxuXHJcbiRpbnB1dC1ncm91cC1hZGRvbi1wYWRkaW5nLXk6ICRpbnB1dC1wYWRkaW5nLXk7XHJcbiRpbnB1dC1ncm91cC1hZGRvbi1wYWRkaW5nLXg6ICRpbnB1dC1wYWRkaW5nLXg7XHJcbiRpbnB1dC1ncm91cC1hZGRvbi1mb250LXdlaWdodDogJGlucHV0LWZvbnQtd2VpZ2h0O1xyXG4kaW5wdXQtZ3JvdXAtYWRkb24tY29sb3I6ICRpbnB1dC1jb2xvcjtcclxuJGlucHV0LWdyb3VwLWFkZG9uLWJnOiAkYm9keS1iZztcclxuJGlucHV0LWdyb3VwLWFkZG9uLWJvcmRlci1jb2xvcjogJGlucHV0LWJvcmRlci1jb2xvcjtcclxuXHJcbiRmb3JtLXNlbGVjdC1wYWRkaW5nLXk6ICRpbnB1dC1wYWRkaW5nLXk7XHJcbiRmb3JtLXNlbGVjdC1wYWRkaW5nLXg6ICRpbnB1dC1wYWRkaW5nLXg7XHJcbiRmb3JtLXNlbGVjdC1mb250LWZhbWlseTogJGlucHV0LWZvbnQtZmFtaWx5O1xyXG4kZm9ybS1zZWxlY3QtZm9udC1zaXplOiAkaW5wdXQtZm9udC1zaXplO1xyXG4kZm9ybS1zZWxlY3QtaW5kaWNhdG9yLXBhZGRpbmc6IDJyZW07IC8vIEV4dHJhIHBhZGRpbmcgdG8gYWNjb3VudCBmb3IgdGhlIHByZXNlbmNlIG9mIHRoZSBiYWNrZ3JvdW5kLWltYWdlIGJhc2VkIGluZGljYXRvclxyXG4kZm9ybS1zZWxlY3QtZm9udC13ZWlnaHQ6ICRpbnB1dC1mb250LXdlaWdodDtcclxuJGZvcm0tc2VsZWN0LWxpbmUtaGVpZ2h0OiAkaW5wdXQtbGluZS1oZWlnaHQ7XHJcbiRmb3JtLXNlbGVjdC1jb2xvcjogJGlucHV0LWNvbG9yO1xyXG4kZm9ybS1zZWxlY3QtZGlzYWJsZWQtY29sb3I6ICRncmF5LTYwMDtcclxuJGZvcm0tc2VsZWN0LWJnOiAkaW5wdXQtYmc7XHJcbiRmb3JtLXNlbGVjdC1kaXNhYmxlZC1iZzogJGdyYXktMjAwO1xyXG4kZm9ybS1zZWxlY3QtZGlzYWJsZWQtYm9yZGVyLWNvbG9yOiAkaW5wdXQtZGlzYWJsZWQtYm9yZGVyLWNvbG9yO1xyXG4kZm9ybS1zZWxlY3QtYmctcG9zaXRpb246IHJpZ2h0ICRmb3JtLXNlbGVjdC1wYWRkaW5nLXggY2VudGVyO1xyXG4kZm9ybS1zZWxlY3QtYmctc2l6ZTogMTZweCAxMnB4OyAvLyBJbiBwaXhlbHMgYmVjYXVzZSBpbWFnZSBkaW1lbnNpb25zXHJcbiRmb3JtLXNlbGVjdC1pbmRpY2F0b3ItY29sb3I6ICRncmF5LTgwMDtcclxuJGZvcm0tc2VsZWN0LWluZGljYXRvcjogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMTYgMTYnPjxwYXRoIGZpbGw9J25vbmUnIHN0cm9rZT0nI3skZm9ybS1zZWxlY3QtaW5kaWNhdG9yLWNvbG9yfScgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBzdHJva2Utd2lkdGg9JzInIGQ9J00yIDVsNiA2IDYtNicvPjwvc3ZnPlwiKTtcclxuXHJcbiRmb3JtLXNlbGVjdC1mZWVkYmFjay1pY29uLXBhZGRpbmctZW5kOiBhZGQoXHJcbiAgMWVtICogMC43NSxcclxuICAoMiAqICRmb3JtLXNlbGVjdC1wYWRkaW5nLXkgKiAwLjc1KSArICRmb3JtLXNlbGVjdC1wYWRkaW5nLXggKyAkZm9ybS1zZWxlY3QtaW5kaWNhdG9yLXBhZGRpbmdcclxuKTtcclxuJGZvcm0tc2VsZWN0LWZlZWRiYWNrLWljb24tcG9zaXRpb246IGNlbnRlciByaWdodCAoJGZvcm0tc2VsZWN0LXBhZGRpbmcteCArICRmb3JtLXNlbGVjdC1pbmRpY2F0b3ItcGFkZGluZyk7XHJcbiRmb3JtLXNlbGVjdC1mZWVkYmFjay1pY29uLXNpemU6ICRpbnB1dC1oZWlnaHQtaW5uZXItaGFsZiAkaW5wdXQtaGVpZ2h0LWlubmVyLWhhbGY7XHJcblxyXG4kZm9ybS1zZWxlY3QtYm9yZGVyLXdpZHRoOiAkaW5wdXQtYm9yZGVyLXdpZHRoO1xyXG4kZm9ybS1zZWxlY3QtYm9yZGVyLWNvbG9yOiAkaW5wdXQtYm9yZGVyLWNvbG9yO1xyXG4kZm9ybS1zZWxlY3QtYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XHJcbiRmb3JtLXNlbGVjdC1ib3gtc2hhZG93OiAkYm94LXNoYWRvdy1pbnNldDtcclxuXHJcbiRmb3JtLXNlbGVjdC1mb2N1cy1ib3JkZXItY29sb3I6ICRpbnB1dC1mb2N1cy1ib3JkZXItY29sb3I7XHJcbiRmb3JtLXNlbGVjdC1mb2N1cy13aWR0aDogJGlucHV0LWZvY3VzLXdpZHRoO1xyXG4kZm9ybS1zZWxlY3QtZm9jdXMtYm94LXNoYWRvdzogMCAwIDAgMXB4ICRjb21wb25lbnQtYWN0aXZlLWJnO1xyXG5cclxuJGZvcm0tc2VsZWN0LXBhZGRpbmcteS1zbTogJGlucHV0LXBhZGRpbmcteS1zbTtcclxuJGZvcm0tc2VsZWN0LXBhZGRpbmcteC1zbTogJGlucHV0LXBhZGRpbmcteC1zbTtcclxuJGZvcm0tc2VsZWN0LWZvbnQtc2l6ZS1zbTogJGlucHV0LWZvbnQtc2l6ZS1zbTtcclxuJGZvcm0tc2VsZWN0LWJvcmRlci1yYWRpdXMtc206ICRpbnB1dC1ib3JkZXItcmFkaXVzLXNtO1xyXG5cclxuJGZvcm0tc2VsZWN0LXBhZGRpbmcteS1sZzogJGlucHV0LXBhZGRpbmcteS1sZztcclxuJGZvcm0tc2VsZWN0LXBhZGRpbmcteC1sZzogJGlucHV0LXBhZGRpbmcteC1sZztcclxuJGZvcm0tc2VsZWN0LWZvbnQtc2l6ZS1sZzogJGlucHV0LWZvbnQtc2l6ZS1sZztcclxuJGZvcm0tc2VsZWN0LWJvcmRlci1yYWRpdXMtbGc6ICRpbnB1dC1ib3JkZXItcmFkaXVzLWxnO1xyXG5cclxuJGZvcm0tc2VsZWN0LXRyYW5zaXRpb246ICRpbnB1dC10cmFuc2l0aW9uO1xyXG4vLyBzY3NzLWRvY3MtZW5kIGZvcm0tc2VsZWN0LXZhcmlhYmxlc1xyXG5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IGZvcm0tcmFuZ2UtdmFyaWFibGVzXHJcbiRmb3JtLXJhbmdlLXRyYWNrLXdpZHRoOiAxMDAlO1xyXG4kZm9ybS1yYW5nZS10cmFjay1oZWlnaHQ6IDAuNXJlbTtcclxuJGZvcm0tcmFuZ2UtdHJhY2stY3Vyc29yOiBwb2ludGVyO1xyXG4kZm9ybS1yYW5nZS10cmFjay1iZzogJGJvZHktYmc7XHJcbiRmb3JtLXJhbmdlLXRyYWNrLWJvcmRlci1yYWRpdXM6IDFyZW07XHJcbiRmb3JtLXJhbmdlLXRyYWNrLWJveC1zaGFkb3c6ICRib3gtc2hhZG93LWluc2V0O1xyXG5cclxuJGZvcm0tcmFuZ2UtdGh1bWItd2lkdGg6IDFyZW07XHJcbiRmb3JtLXJhbmdlLXRodW1iLWhlaWdodDogJGZvcm0tcmFuZ2UtdGh1bWItd2lkdGg7XHJcbiRmb3JtLXJhbmdlLXRodW1iLWJnOiAkY29tcG9uZW50LWFjdGl2ZS1iZztcclxuJGZvcm0tcmFuZ2UtdGh1bWItYm9yZGVyOiAwO1xyXG4kZm9ybS1yYW5nZS10aHVtYi1ib3JkZXItcmFkaXVzOiAxcmVtO1xyXG4kZm9ybS1yYW5nZS10aHVtYi1ib3gtc2hhZG93OiAwIDAuMXJlbSAwLjI1cmVtIHJnYmEoJGJsYWNrLCAwLjEpO1xyXG4kZm9ybS1yYW5nZS10aHVtYi1mb2N1cy1ib3gtc2hhZG93OlxyXG4gIDAgMCAwIDFweCAkYm9keS1iZyxcclxuICAkaW5wdXQtZm9jdXMtYm94LXNoYWRvdztcclxuJGZvcm0tcmFuZ2UtdGh1bWItZm9jdXMtYm94LXNoYWRvdy13aWR0aDogJGlucHV0LWZvY3VzLXdpZHRoOyAvLyBGb3IgZm9jdXMgYm94IHNoYWRvdyBpc3N1ZSBpbiBFZGdlXHJcbiRmb3JtLXJhbmdlLXRodW1iLWFjdGl2ZS1iZzogcmdiYSgje3ZhcigtLWJzLXByaW1hcnktcmdiKX0sIDAuOCk7XHJcbiRmb3JtLXJhbmdlLXRodW1iLWRpc2FibGVkLWJnOiAkZ3JheS01MDA7XHJcbiRmb3JtLXJhbmdlLXRodW1iLXRyYW5zaXRpb246XHJcbiAgYmFja2dyb3VuZC1jb2xvciAwLjE1cyBlYXNlLWluLW91dCxcclxuICBib3JkZXItY29sb3IgMC4xNXMgZWFzZS1pbi1vdXQsXHJcbiAgYm94LXNoYWRvdyAwLjE1cyBlYXNlLWluLW91dDtcclxuXHJcbiRmb3JtLWZpbGUtYnV0dG9uLWNvbG9yOiAkaW5wdXQtY29sb3I7XHJcbiRmb3JtLWZpbGUtYnV0dG9uLWJnOiAkaW5wdXQtZ3JvdXAtYWRkb24tYmc7XHJcbiRmb3JtLWZpbGUtYnV0dG9uLWhvdmVyLWJnOiBzaGFkZS1jb2xvcigkZm9ybS1maWxlLWJ1dHRvbi1iZywgNSUpO1xyXG5cclxuLy8kZm9ybS1mbG9hdGluZy1oZWlnaHQ6ICAgICAgICAgICAgYXV0bztcclxuLy8kZm9ybS1mbG9hdGluZy1wYWRkaW5nLXg6ICAgICAgICAgJGlucHV0LXBhZGRpbmcteCA7XHJcbi8vJGZvcm0tZmxvYXRpbmctcGFkZGluZy15OiAgICAgICAgIDFyZW0gO1xyXG4vLyRmb3JtLWZsb2F0aW5nLWlucHV0LXBhZGRpbmctdDogICAkaW5wdXQtcGFkZGluZy15IDtcclxuLy8kZm9ybS1mbG9hdGluZy1pbnB1dC1wYWRkaW5nLWI6ICAgJGlucHV0LXBhZGRpbmcteSA7XHJcbiRmb3JtLWZsb2F0aW5nLWxhYmVsLW9wYWNpdHk6IDE7XHJcbi8vJGZvcm0tZmxvYXRpbmctbGFiZWwtdHJhbnNmb3JtOiAgIHNjYWxlKDAuODUpIHRyYW5zbGF0ZVkoLTEuMnJlbSkgdHJhbnNsYXRlWCgwLjQ1cmVtKTtcclxuLy8kZm9ybS1mbG9hdGluZy10cmFuc2l0aW9uOiAgICAgICAgb3BhY2l0eSAuMXMgZWFzZS1pbi1vdXQsIHRyYW5zZm9ybSAuMXMgZWFzZS1pbi1vdXQgO1xyXG5cclxuLy8gRm9ybSB2YWxpZGF0aW9uXHJcblxyXG4kZm9ybS1mZWVkYmFjay1tYXJnaW4tdG9wOiAkZm9ybS10ZXh0LW1hcmdpbi10b3A7XHJcbiRmb3JtLWZlZWRiYWNrLWZvbnQtc2l6ZTogJGZvcm0tdGV4dC1mb250LXNpemU7XHJcbiRmb3JtLWZlZWRiYWNrLWZvbnQtc3R5bGU6ICRmb3JtLXRleHQtZm9udC1zdHlsZTtcclxuJGZvcm0tZmVlZGJhY2stdmFsaWQtY29sb3I6ICRzdWNjZXNzO1xyXG4kZm9ybS1mZWVkYmFjay1pbnZhbGlkLWNvbG9yOiAkZGFuZ2VyO1xyXG5cclxuJGZvcm0tZmVlZGJhY2staWNvbi12YWxpZC1jb2xvcjogJGZvcm0tZmVlZGJhY2stdmFsaWQtY29sb3I7XHJcbiRmb3JtLWZlZWRiYWNrLWljb24tdmFsaWQ6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nMCAwIDggOCc+PHBhdGggZmlsbD0nI3skZm9ybS1mZWVkYmFjay1pY29uLXZhbGlkLWNvbG9yfScgZD0nTTIuMyA2LjczLjYgNC41M2MtLjQtMS4wNC40Ni0xLjQgMS4xLS44bDEuMSAxLjQgMy40LTMuOGMuNi0uNjMgMS42LS4yNyAxLjIuN2wtNCA0LjZjLS40My41LS44LjQtMS4xLjF6Jy8+PC9zdmc+XCIpO1xyXG4kZm9ybS1mZWVkYmFjay1pY29uLWludmFsaWQtY29sb3I6ICRmb3JtLWZlZWRiYWNrLWludmFsaWQtY29sb3I7XHJcbiRmb3JtLWZlZWRiYWNrLWljb24taW52YWxpZDogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMTIgMTInIHdpZHRoPScxMicgaGVpZ2h0PScxMicgZmlsbD0nbm9uZScgc3Ryb2tlPScjeyRmb3JtLWZlZWRiYWNrLWljb24taW52YWxpZC1jb2xvcn0nPjxjaXJjbGUgY3g9JzYnIGN5PSc2JyByPSc0LjUnLz48cGF0aCBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBkPSdNNS44IDMuNmguNEw2IDYuNXonLz48Y2lyY2xlIGN4PSc2JyBjeT0nOC4yJyByPScuNicgZmlsbD0nI3skZm9ybS1mZWVkYmFjay1pY29uLWludmFsaWQtY29sb3J9JyBzdHJva2U9J25vbmUnLz48L3N2Zz5cIik7XHJcbi8vIHNjc3MtZG9jcy1lbmQgZm9ybS1mZWVkYmFjay12YXJpYWJsZXNcclxuXHJcbi8vIHNjc3MtZG9jcy1zdGFydCBmb3JtLXZhbGlkYXRpb24tc3RhdGVzXHJcbiRmb3JtLXZhbGlkYXRpb24tc3RhdGVzOiAoXHJcbiAgJ3ZhbGlkJzogKFxyXG4gICAgJ2NvbG9yJzogJGZvcm0tZmVlZGJhY2stdmFsaWQtY29sb3IsXHJcbiAgICAnaWNvbic6ICRmb3JtLWZlZWRiYWNrLWljb24tdmFsaWRcclxuICApLFxyXG4gICdpbnZhbGlkJzogKFxyXG4gICAgJ2NvbG9yJzogJGZvcm0tZmVlZGJhY2staW52YWxpZC1jb2xvcixcclxuICAgICdpY29uJzogJGZvcm0tZmVlZGJhY2staWNvbi1pbnZhbGlkXHJcbiAgKVxyXG4pO1xyXG4vLyBzY3NzLWRvY3MtZW5kIGZvcm0tdmFsaWRhdGlvbi1zdGF0ZXNcclxuXHJcbi8vIFotaW5kZXggbWFzdGVyIGxpc3RcclxuLy9cclxuLy8gV2FybmluZzogQXZvaWQgY3VzdG9taXppbmcgdGhlc2UgdmFsdWVzLiBUaGV5J3JlIHVzZWQgZm9yIGEgYmlyZCdzIGV5ZSB2aWV3XHJcbi8vIG9mIGNvbXBvbmVudHMgZGVwZW5kZW50IG9uIHRoZSB6LWF4aXMgYW5kIGFyZSBkZXNpZ25lZCB0byBhbGwgd29yayB0b2dldGhlci5cclxuXHJcbi8vIHNjc3MtZG9jcy1zdGFydCB6aW5kZXgtc3RhY2tcclxuJHppbmRleC1kcm9wZG93bjogMTAyNjtcclxuJHppbmRleC1zdGlja3k6IDEwMjA7XHJcbiR6aW5kZXgtZml4ZWQ6IDEwMzA7XHJcbiR6aW5kZXgtb2ZmY2FudmFzLWJhY2tkcm9wOiAxMDQwO1xyXG4kemluZGV4LW9mZmNhbnZhczogMTA0NTtcclxuJHppbmRleC1tb2RhbC1iYWNrZHJvcDogMTA1MDtcclxuJHppbmRleC1tb2RhbDogMTA1NTtcclxuJHppbmRleC1wb3BvdmVyOiAxMDcwO1xyXG4kemluZGV4LXRvb2x0aXA6IDEwODA7XHJcbiR6aW5kZXgtdG9hc3Q6IDEwOTA7XHJcbi8vIHNjc3MtZG9jcy1lbmQgemluZGV4LXN0YWNrXHJcblxyXG4vLyBOYXZzXHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgbmF2LXZhcmlhYmxlc1xyXG4kbmF2LWxpbmstcGFkZGluZy15OiAwLjVyZW07XHJcbiRuYXYtbGluay1wYWRkaW5nLXg6IDFyZW07XHJcbiRuYXYtbGluay1mb250LXNpemU6IG51bGw7XHJcbiRuYXYtbGluay1mb250LXdlaWdodDogbnVsbDtcclxuJG5hdi1saW5rLWNvbG9yOiBudWxsO1xyXG4kbmF2LWxpbmstY29sb3I6IHZhcigtLSN7JHByZWZpeH1saW5rLWNvbG9yKTtcclxuJG5hdi1saW5rLWhvdmVyLWNvbG9yOiB2YXIoLS0jeyRwcmVmaXh9bGluay1ob3Zlci1jb2xvcik7XHJcbiRuYXYtbGluay10cmFuc2l0aW9uOlxyXG4gIGNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxyXG4gIGJhY2tncm91bmQtY29sb3IgMC4xNXMgZWFzZS1pbi1vdXQsXHJcbiAgYm9yZGVyLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0O1xyXG4kbmF2LWxpbmstZGlzYWJsZWQtY29sb3I6ICRncmF5LTYwMDtcclxuXHJcbiRuYXYtdGFicy1ib3JkZXItY29sb3I6IHZhcigtLWJzLWJvcmRlci1jb2xvcik7XHJcbiRuYXYtdGFicy1ib3JkZXItd2lkdGg6ICRib3JkZXItd2lkdGg7XHJcbiRuYXYtdGFicy1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cztcclxuJG5hdi10YWJzLWxpbmstaG92ZXItYm9yZGVyLWNvbG9yOiB2YXIoLS1icy1ib3JkZXItY29sb3IpIHZhcigtLWJzLWJvcmRlci1jb2xvcikgJG5hdi10YWJzLWJvcmRlci1jb2xvcjtcclxuJG5hdi10YWJzLWxpbmstYWN0aXZlLWNvbG9yOiB2YXIoLS1icy1ib2R5LWNvbG9yKTtcclxuJG5hdi10YWJzLWxpbmstYWN0aXZlLWJnOiB2YXIoLS1icy1ib2R5LWJnKTtcclxuJG5hdi10YWJzLWxpbmstYWN0aXZlLWJvcmRlci1jb2xvcjogdmFyKC0tYnMtYm9yZGVyLWNvbG9yKSB2YXIoLS1icy1ib3JkZXItY29sb3IpICRuYXYtdGFicy1saW5rLWFjdGl2ZS1iZztcclxuXHJcbiRuYXYtcGlsbHMtYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XHJcbiRuYXYtcGlsbHMtbGluay1hY3RpdmUtY29sb3I6ICRjb21wb25lbnQtYWN0aXZlLWNvbG9yO1xyXG4kbmF2LXBpbGxzLWxpbmstYWN0aXZlLWJnOiAkY29tcG9uZW50LWFjdGl2ZS1iZztcclxuLy8gc2Nzcy1kb2NzLWVuZCBuYXYtdmFyaWFibGVzXHJcblxyXG4vLyBOYXZiYXJcclxuXHJcbiRuYXZiYXItcGFkZGluZy15OiBjYWxjKCN7JHNwYWNlcn0gLyAyKTtcclxuJG5hdmJhci1wYWRkaW5nLXg6IG51bGw7XHJcblxyXG4kbmF2YmFyLW5hdi1saW5rLXBhZGRpbmcteDogMC41cmVtO1xyXG5cclxuJG5hdmJhci1icmFuZC1mb250LXNpemU6ICRmb250LXNpemUtbGc7XHJcbi8vIENvbXB1dGUgdGhlIG5hdmJhci1icmFuZCBwYWRkaW5nLXkgc28gdGhlIG5hdmJhci1icmFuZCB3aWxsIGhhdmUgdGhlIHNhbWUgaGVpZ2h0IGFzIG5hdmJhci10ZXh0IGFuZCBuYXYtbGlua1xyXG4kbmF2LWxpbmstaGVpZ2h0OiAkZm9udC1zaXplLWJhc2UgKiAkbGluZS1oZWlnaHQtYmFzZSArICRuYXYtbGluay1wYWRkaW5nLXkgKiAyO1xyXG4kbmF2YmFyLWJyYW5kLWhlaWdodDogJG5hdmJhci1icmFuZC1mb250LXNpemUgKiAkbGluZS1oZWlnaHQtYmFzZTtcclxuJG5hdmJhci1icmFuZC1wYWRkaW5nLXk6IGNhbGMoKCRuYXYtbGluay1oZWlnaHQgLSAkbmF2YmFyLWJyYW5kLWhlaWdodCkgLyAyKTtcclxuJG5hdmJhci1icmFuZC1tYXJnaW4tZW5kOiAxcmVtO1xyXG5cclxuJG5hdmJhci10b2dnbGVyLXBhZGRpbmcteTogMC4yNXJlbTtcclxuJG5hdmJhci10b2dnbGVyLXBhZGRpbmcteDogMC43NXJlbTtcclxuJG5hdmJhci10b2dnbGVyLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1sZztcclxuJG5hdmJhci10b2dnbGVyLWJvcmRlci1yYWRpdXM6ICRidG4tYm9yZGVyLXJhZGl1cztcclxuJG5hdmJhci10b2dnbGVyLWZvY3VzLXdpZHRoOiAkYnRuLWZvY3VzLXdpZHRoO1xyXG4kbmF2YmFyLXRvZ2dsZXItdHJhbnNpdGlvbjogYm94LXNoYWRvdyAwLjE1cyBlYXNlLWluLW91dDtcclxuXHJcbiRuYXZiYXItbGlnaHQtY29sb3I6IHJnYmEoJGJsYWNrLCAwLjU1KTtcclxuJG5hdmJhci1saWdodC1ob3Zlci1jb2xvcjogcmdiYSgkYmxhY2ssIDAuNyk7XHJcbiRuYXZiYXItbGlnaHQtYWN0aXZlLWNvbG9yOiByZ2JhKCRibGFjaywgMC45KTtcclxuJG5hdmJhci1saWdodC1kaXNhYmxlZC1jb2xvcjogcmdiYSgkYmxhY2ssIDAuMyk7XHJcbiRuYXZiYXItbGlnaHQtdG9nZ2xlci1pY29uLWJnOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAzMCAzMCc+PHBhdGggc3Ryb2tlPScjeyRuYXZiYXItbGlnaHQtY29sb3J9JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1taXRlcmxpbWl0PScxMCcgc3Ryb2tlLXdpZHRoPScyJyBkPSdNNCA3aDIyTTQgMTVoMjJNNCAyM2gyMicvPjwvc3ZnPlwiKTtcclxuJG5hdmJhci1saWdodC10b2dnbGVyLWJvcmRlci1jb2xvcjogcmdiYSgkYmxhY2ssIDAuMSk7XHJcbiRuYXZiYXItbGlnaHQtYnJhbmQtY29sb3I6ICRuYXZiYXItbGlnaHQtYWN0aXZlLWNvbG9yO1xyXG4kbmF2YmFyLWxpZ2h0LWJyYW5kLWhvdmVyLWNvbG9yOiAkbmF2YmFyLWxpZ2h0LWFjdGl2ZS1jb2xvcjtcclxuLy8gc2Nzcy1kb2NzLWVuZCBuYXZiYXItdmFyaWFibGVzXHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgbmF2YmFyLWRhcmstdmFyaWFibGVzXHJcbiRuYXZiYXItZGFyay1jb2xvcjogcmdiYSgkd2hpdGUsIDAuNTUpO1xyXG4kbmF2YmFyLWRhcmstaG92ZXItY29sb3I6IHJnYmEoJHdoaXRlLCAwLjc1KTtcclxuJG5hdmJhci1kYXJrLWFjdGl2ZS1jb2xvcjogJHdoaXRlO1xyXG4kbmF2YmFyLWRhcmstZGlzYWJsZWQtY29sb3I6IHJnYmEoJHdoaXRlLCAwLjI1KTtcclxuJG5hdmJhci1kYXJrLXRvZ2dsZXItaWNvbi1iZzogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMzAgMzAnPjxwYXRoIHN0cm9rZT0nI3skbmF2YmFyLWRhcmstY29sb3J9JyBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1taXRlcmxpbWl0PScxMCcgc3Ryb2tlLXdpZHRoPScyJyBkPSdNNCA3aDIyTTQgMTVoMjJNNCAyM2gyMicvPjwvc3ZnPlwiKTtcclxuJG5hdmJhci1kYXJrLXRvZ2dsZXItYm9yZGVyLWNvbG9yOiByZ2JhKCR3aGl0ZSwgMC4xKTtcclxuJG5hdmJhci1kYXJrLWJyYW5kLWNvbG9yOiAkbmF2YmFyLWRhcmstYWN0aXZlLWNvbG9yO1xyXG4kbmF2YmFyLWRhcmstYnJhbmQtaG92ZXItY29sb3I6ICRuYXZiYXItZGFyay1hY3RpdmUtY29sb3I7XHJcbi8vIHNjc3MtZG9jcy1lbmQgbmF2YmFyLWRhcmstdmFyaWFibGVzXHJcblxyXG4vLyBEcm9wZG93bnNcclxuLy9cclxuLy8gRHJvcGRvd24gbWVudSBjb250YWluZXIgYW5kIGNvbnRlbnRzLlxyXG5cclxuJGRyb3Bkb3duLW1pbi13aWR0aDogMTJyZW07XHJcbiRkcm9wZG93bi1wYWRkaW5nLXg6IDAuNXJlbTtcclxuJGRyb3Bkb3duLXBhZGRpbmcteTogMC41cmVtO1xyXG4kZHJvcGRvd24tc3BhY2VyOiAwLjEyNXJlbTtcclxuJGRyb3Bkb3duLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1iYXNlO1xyXG4kZHJvcGRvd24tY29sb3I6ICRib2R5LWNvbG9yO1xyXG4kZHJvcGRvd24tYmc6ICR3aGl0ZTtcclxuJGRyb3Bkb3duLWJvcmRlci1jb2xvcjogcmdiYSgkYmxhY2ssIDAuMTUpO1xyXG4kZHJvcGRvd24tYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XHJcbiRkcm9wZG93bi1ib3JkZXItd2lkdGg6IDA7XHJcbiRkcm9wZG93bi1pbm5lci1ib3JkZXItcmFkaXVzOiBzdWJ0cmFjdCgkZHJvcGRvd24tYm9yZGVyLXJhZGl1cywgJGRyb3Bkb3duLWJvcmRlci13aWR0aCk7XHJcbiRkcm9wZG93bi1kaXZpZGVyLWJnOiAkZ3JheS0yMDA7XHJcbiRkcm9wZG93bi1kaXZpZGVyLW1hcmdpbi15OiBjYWxjKCRzcGFjZXIgLyAyKTtcclxuJGRyb3Bkb3duLWJveC1zaGFkb3c6IDAgMC41cmVtIDFyZW0gcmdiYSgkYmxhY2ssIDAuMTc1KTtcclxuXHJcbiRkcm9wZG93bi1saW5rLWNvbG9yOiAkYm9keS1jb2xvcjtcclxuJGRyb3Bkb3duLWxpbmstaG92ZXItY29sb3I6ICRwcmltYXJ5O1xyXG4kZHJvcGRvd24tbGluay1ob3Zlci1iZzogc2hpZnQtY29sb3IoJHByaW1hcnksIC04MCUpO1xyXG5cclxuJGRyb3Bkb3duLWxpbmstYWN0aXZlLWNvbG9yOiAkcHJpbWFyeTtcclxuJGRyb3Bkb3duLWxpbmstYWN0aXZlLWJnOiBzaGlmdC1jb2xvcigkcHJpbWFyeSwgLTgwJSk7XHJcblxyXG4kZHJvcGRvd24tbGluay1kaXNhYmxlZC1jb2xvcjogJGdyYXktNjAwO1xyXG5cclxuJGRyb3Bkb3duLWl0ZW0tcGFkZGluZy15OiAwLjY1cmVtO1xyXG4kZHJvcGRvd24taXRlbS1wYWRkaW5nLXg6IDEuNXJlbTtcclxuXHJcbiRkcm9wZG93bi1oZWFkZXItY29sb3I6ICRncmF5LTYwMDtcclxuJGRyb3Bkb3duLWhlYWRlci1wYWRkaW5nLXg6ICRkcm9wZG93bi1pdGVtLXBhZGRpbmcteDtcclxuJGRyb3Bkb3duLWhlYWRlci1wYWRkaW5nLXk6ICRkcm9wZG93bi1wYWRkaW5nLXk7XHJcbi8vIGZ1c3YtZGlzYWJsZVxyXG4kZHJvcGRvd24taGVhZGVyLXBhZGRpbmc6ICRkcm9wZG93bi1oZWFkZXItcGFkZGluZy15ICRkcm9wZG93bi1oZWFkZXItcGFkZGluZy14OyAvLyBEZXByZWNhdGVkIGluIHY1LjIuMFxyXG4vLyBmdXN2LWVuYWJsZVxyXG4vLyBzY3NzLWRvY3MtZW5kIGRyb3Bkb3duLXZhcmlhYmxlc1xyXG5cclxuJGRyb3Bkb3duLWRhcmstY29sb3I6ICRncmF5LTMwMDtcclxuJGRyb3Bkb3duLWRhcmstYmc6ICRncmF5LTgwMDtcclxuJGRyb3Bkb3duLWRhcmstYm9yZGVyLWNvbG9yOiAkZHJvcGRvd24tYm9yZGVyLWNvbG9yO1xyXG4kZHJvcGRvd24tZGFyay1kaXZpZGVyLWJnOiAkZHJvcGRvd24tZGl2aWRlci1iZztcclxuJGRyb3Bkb3duLWRhcmstYm94LXNoYWRvdzogbnVsbDtcclxuJGRyb3Bkb3duLWRhcmstbGluay1jb2xvcjogJGRyb3Bkb3duLWRhcmstY29sb3I7XHJcbiRkcm9wZG93bi1kYXJrLWxpbmstaG92ZXItY29sb3I6ICR3aGl0ZTtcclxuJGRyb3Bkb3duLWRhcmstbGluay1ob3Zlci1iZzogcmdiYSgkd2hpdGUsIDAuMTUpO1xyXG4kZHJvcGRvd24tZGFyay1saW5rLWFjdGl2ZS1jb2xvcjogJGRyb3Bkb3duLWxpbmstYWN0aXZlLWNvbG9yO1xyXG4kZHJvcGRvd24tZGFyay1saW5rLWFjdGl2ZS1iZzogJGRyb3Bkb3duLWxpbmstYWN0aXZlLWJnO1xyXG4kZHJvcGRvd24tZGFyay1saW5rLWRpc2FibGVkLWNvbG9yOiAkZ3JheS01MDA7XHJcbiRkcm9wZG93bi1kYXJrLWhlYWRlci1jb2xvcjogJGdyYXktNTAwO1xyXG5cclxuLy8gUGFnaW5hdGlvblxyXG5cclxuJHBhZ2luYXRpb24tcGFkZGluZy15OiAwLjM3NXJlbTtcclxuJHBhZ2luYXRpb24tcGFkZGluZy14OiAwLjc1cmVtO1xyXG4kcGFnaW5hdGlvbi1wYWRkaW5nLXktc206IDAuMjVyZW07XHJcbiRwYWdpbmF0aW9uLXBhZGRpbmcteC1zbTogMC41cmVtO1xyXG4kcGFnaW5hdGlvbi1wYWRkaW5nLXktbGc6IDAuNzVyZW07XHJcbiRwYWdpbmF0aW9uLXBhZGRpbmcteC1sZzogMS41cmVtO1xyXG5cclxuJHBhZ2luYXRpb24tZm9udC1zaXplOiAkZm9udC1zaXplLWJhc2U7XHJcblxyXG4kcGFnaW5hdGlvbi1jb2xvcjogdmFyKC0tI3skcHJlZml4fWxpbmstY29sb3IpO1xyXG4kcGFnaW5hdGlvbi1iZzogJHdoaXRlO1xyXG4kcGFnaW5hdGlvbi1ib3JkZXItd2lkdGg6ICRib3JkZXItd2lkdGg7XHJcbiRwYWdpbmF0aW9uLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xyXG4kcGFnaW5hdGlvbi1ib3JkZXItd2lkdGg6ICRib3JkZXItd2lkdGg7XHJcbiRwYWdpbmF0aW9uLW1hcmdpbi1zdGFydDogKCRwYWdpbmF0aW9uLWJvcmRlci13aWR0aCAqIC0xKTtcclxuJHBhZ2luYXRpb24tYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDA7XHJcblxyXG4kcGFnaW5hdGlvbi1mb2N1cy1jb2xvcjogdmFyKC0tI3skcHJlZml4fWxpbmstaG92ZXItY29sb3IpO1xyXG4kcGFnaW5hdGlvbi1mb2N1cy1iZzogJGdyYXktMjAwO1xyXG4kcGFnaW5hdGlvbi1mb2N1cy1ib3gtc2hhZG93OiAkaW5wdXQtYnRuLWZvY3VzLWJveC1zaGFkb3c7XHJcbiRwYWdpbmF0aW9uLWZvY3VzLW91dGxpbmU6IDA7XHJcblxyXG4kcGFnaW5hdGlvbi1ob3Zlci1jb2xvcjogdmFyKC0tI3skcHJlZml4fWxpbmstaG92ZXItY29sb3IpO1xyXG4kcGFnaW5hdGlvbi1ob3Zlci1iZzogJGdyYXktMjAwO1xyXG4kcGFnaW5hdGlvbi1ob3Zlci1ib3JkZXItY29sb3I6ICRncmF5LTMwMDtcclxuXHJcbiRwYWdpbmF0aW9uLWFjdGl2ZS1jb2xvcjogJGNvbXBvbmVudC1hY3RpdmUtY29sb3I7XHJcbiRwYWdpbmF0aW9uLWFjdGl2ZS1iZzogdmFyKC0tYnMtcHJpbWFyeSk7XHJcbiRwYWdpbmF0aW9uLWFjdGl2ZS1ib3JkZXItY29sb3I6IHZhcigtLWJzLXByaW1hcnkpO1xyXG5cclxuJHBhZ2luYXRpb24tZGlzYWJsZWQtY29sb3I6ICRncmF5LTYwMDtcclxuJHBhZ2luYXRpb24tZGlzYWJsZWQtYmc6ICR3aGl0ZTtcclxuJHBhZ2luYXRpb24tZGlzYWJsZWQtYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDA7XHJcblxyXG4kcGFnaW5hdGlvbi10cmFuc2l0aW9uOlxyXG4gIGNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxyXG4gIGJhY2tncm91bmQtY29sb3IgMC4xNXMgZWFzZS1pbi1vdXQsXHJcbiAgYm9yZGVyLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0LFxyXG4gIGJveC1zaGFkb3cgMC4xNXMgZWFzZS1pbi1vdXQ7XHJcblxyXG4kcGFnaW5hdGlvbi1ib3JkZXItcmFkaXVzLXNtOiAkYm9yZGVyLXJhZGl1cy1zbTtcclxuJHBhZ2luYXRpb24tYm9yZGVyLXJhZGl1cy1sZzogJGJvcmRlci1yYWRpdXMtbGc7XHJcbi8vIHNjc3MtZG9jcy1lbmQgcGFnaW5hdGlvbi12YXJpYWJsZXNcclxuXHJcbi8vIFBsYWNlaG9sZGVyc1xyXG5cclxuLy8gc2Nzcy1kb2NzLXN0YXJ0IHBsYWNlaG9sZGVyc1xyXG4kcGxhY2Vob2xkZXItb3BhY2l0eS1tYXg6IDAuNTtcclxuJHBsYWNlaG9sZGVyLW9wYWNpdHktbWluOiAwLjI7XHJcbi8vIHNjc3MtZG9jcy1lbmQgcGxhY2Vob2xkZXJzXHJcblxyXG4vLyBDYXJkc1xyXG4kY2FyZC1zcGFjZXIteTogMjVweDtcclxuJGNhcmQtc3BhY2VyLXg6IDI1cHg7XHJcbiRjYXJkLXRpdGxlLXNwYWNlci15OiBjYWxjKCRzcGFjZXIgLyAyKTtcclxuJGNhcmQtYm9yZGVyLXdpZHRoOiAwcHg7XHJcbiRjYXJkLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xyXG4kY2FyZC1ib3JkZXItY29sb3I6ICRib3JkZXItY29sb3I7XHJcbiRjYXJkLWJveC1zaGFkb3c6IG5vbmU7XHJcbiRjYXJkLWlubmVyLWJvcmRlci1yYWRpdXM6IGNhbGMoI3skY2FyZC1ib3JkZXItcmFkaXVzfSAtICN7JGNhcmQtYm9yZGVyLXdpZHRofSk7XHJcbiRjYXJkLWNhcC1wYWRkaW5nLXk6IDI1cHg7XHJcbiRjYXJkLWNhcC1wYWRkaW5nLXg6IDI1cHg7XHJcbiRjYXJkLWNhcC1iZzogdHJhbnNwYXJlbnQ7XHJcbiRjYXJkLWNhcC1jb2xvcjogbnVsbDtcclxuJGNhcmQtaGVpZ2h0OiBudWxsO1xyXG4kY2FyZC1jb2xvcjogbnVsbDtcclxuJGNhcmQtYmc6ICR3aGl0ZTtcclxuXHJcbiRjYXJkLWltZy1vdmVybGF5LXBhZGRpbmc6IDEuMjVyZW07XHJcblxyXG4kY2FyZC1ncm91cC1tYXJnaW46IGNhbGMoJGdyaWQtZ3V0dGVyLXdpZHRoIC8gMik7XHJcblxyXG4vLyBBY2NvcmRpb25cclxuJGFjY29yZGlvbi1wYWRkaW5nLXk6IDFyZW07XHJcbiRhY2NvcmRpb24tcGFkZGluZy14OiAxLjI1cmVtO1xyXG4kYWNjb3JkaW9uLWNvbG9yOiAkYm9keS1jb2xvcjtcclxuJGFjY29yZGlvbi1iZzogJGNhcmQtYmc7XHJcbiRhY2NvcmRpb24tYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xyXG4kYWNjb3JkaW9uLWJvcmRlci1jb2xvcjogdmFyKC0tI3skcHJlZml4fWJvcmRlci1jb2xvcik7XHJcbiRhY2NvcmRpb24tYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XHJcbiRhY2NvcmRpb24taW5uZXItYm9yZGVyLXJhZGl1czogc3VidHJhY3QoJGFjY29yZGlvbi1ib3JkZXItcmFkaXVzLCAkYWNjb3JkaW9uLWJvcmRlci13aWR0aCk7XHJcblxyXG4kYWNjb3JkaW9uLWJvZHktcGFkZGluZy15OiAkYWNjb3JkaW9uLXBhZGRpbmcteTtcclxuJGFjY29yZGlvbi1ib2R5LXBhZGRpbmcteDogJGFjY29yZGlvbi1wYWRkaW5nLXg7XHJcblxyXG4kYWNjb3JkaW9uLWJ1dHRvbi1wYWRkaW5nLXk6ICRhY2NvcmRpb24tcGFkZGluZy15O1xyXG4kYWNjb3JkaW9uLWJ1dHRvbi1wYWRkaW5nLXg6ICRhY2NvcmRpb24tcGFkZGluZy14O1xyXG4kYWNjb3JkaW9uLWJ1dHRvbi1jb2xvcjogJGFjY29yZGlvbi1jb2xvcjtcclxuJGFjY29yZGlvbi1idXR0b24tYmc6IHZhcigtLSN7JHByZWZpeH1hY2NvcmRpb24tYmcpO1xyXG4kYWNjb3JkaW9uLXRyYW5zaXRpb246XHJcbiAgJGJ0bi10cmFuc2l0aW9uLFxyXG4gIGJvcmRlci1yYWRpdXMgMC4xNXMgZWFzZTtcclxuJGFjY29yZGlvbi1idXR0b24tYWN0aXZlLWJnOiB0aW50LWNvbG9yKCRwcmltYXJ5LCA5MCUpO1xyXG4kYWNjb3JkaW9uLWJ1dHRvbi1hY3RpdmUtY29sb3I6IHNoYWRlLWNvbG9yKCRwcmltYXJ5LCAxMCUpO1xyXG5cclxuJGFjY29yZGlvbi1idXR0b24tZm9jdXMtYm9yZGVyLWNvbG9yOiAkaW5wdXQtZm9jdXMtYm9yZGVyLWNvbG9yO1xyXG4kYWNjb3JkaW9uLWJ1dHRvbi1mb2N1cy1ib3gtc2hhZG93OiAkYnRuLWZvY3VzLWJveC1zaGFkb3c7XHJcblxyXG4kYWNjb3JkaW9uLWljb24td2lkdGg6IDEuMjVyZW07XHJcbiRhY2NvcmRpb24taWNvbi1jb2xvcjogJGFjY29yZGlvbi1jb2xvcjtcclxuJGFjY29yZGlvbi1pY29uLWFjdGl2ZS1jb2xvcjogJGFjY29yZGlvbi1idXR0b24tYWN0aXZlLWNvbG9yO1xyXG4kYWNjb3JkaW9uLWljb24tdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZS1pbi1vdXQ7XHJcbiRhY2NvcmRpb24taWNvbi10cmFuc2Zvcm06IHJvdGF0ZSgxODBkZWcpO1xyXG5cclxuJGFjY29yZGlvbi1idXR0b24taWNvbjogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMTYgMTYnIGZpbGw9JyN7JGFjY29yZGlvbi1pY29uLWNvbG9yfSc+PHBhdGggZmlsbC1ydWxlPSdldmVub2RkJyBkPSdNMS42NDYgNC42NDZhLjUuNSAwIDAgMSAuNzA4IDBMOCAxMC4yOTNsNS42NDYtNS42NDdhLjUuNSAwIDAgMSAuNzA4LjcwOGwtNiA2YS41LjUgMCAwIDEtLjcwOCAwbC02LTZhLjUuNSAwIDAgMSAwLS43MDh6Jy8+PC9zdmc+XCIpO1xyXG4kYWNjb3JkaW9uLWJ1dHRvbi1hY3RpdmUtaWNvbjogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMTYgMTYnIGZpbGw9JyN7JGFjY29yZGlvbi1pY29uLWFjdGl2ZS1jb2xvcn0nPjxwYXRoIGZpbGwtcnVsZT0nZXZlbm9kZCcgZD0nTTEuNjQ2IDQuNjQ2YS41LjUgMCAwIDEgLjcwOCAwTDggMTAuMjkzbDUuNjQ2LTUuNjQ3YS41LjUgMCAwIDEgLjcwOC43MDhsLTYgNmEuNS41IDAgMCAxLS43MDggMGwtNi02YS41LjUgMCAwIDEgMC0uNzA4eicvPjwvc3ZnPlwiKTtcclxuXHJcbi8vIFRvb2x0aXBzXHJcblxyXG4kdG9vbHRpcC1mb250LXNpemU6ICRmb250LXNpemUtc207XHJcbiR0b29sdGlwLW1heC13aWR0aDogMjAwcHg7XHJcbiR0b29sdGlwLWNvbG9yOiAkd2hpdGU7XHJcbiR0b29sdGlwLWJnOiAkYmxhY2s7XHJcbiR0b29sdGlwLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xyXG4kdG9vbHRpcC1vcGFjaXR5OiAwLjk7XHJcbiR0b29sdGlwLXBhZGRpbmcteTogMC4yNXJlbTtcclxuJHRvb2x0aXAtcGFkZGluZy14OiAwLjVyZW07XHJcbiR0b29sdGlwLW1hcmdpbjogMDtcclxuXHJcbiR0b29sdGlwLWFycm93LXdpZHRoOiAwLjhyZW07XHJcbiR0b29sdGlwLWFycm93LWhlaWdodDogMC40cmVtO1xyXG4vLyBmdXN2LWRpc2FibGVcclxuJHRvb2x0aXAtYXJyb3ctY29sb3I6IG51bGw7IC8vIERlcHJlY2F0ZWQgaW4gQm9vdHN0cmFwIDUuMi4wIGZvciBDU1MgdmFyaWFibGVzXHJcbi8vIGZ1c3YtZW5hYmxlXHJcbi8vIHNjc3MtZG9jcy1lbmQgdG9vbHRpcC12YXJpYWJsZXNcclxuXHJcbi8vIEZvcm0gdG9vbHRpcHMgbXVzdCBjb21lIGFmdGVyIHJlZ3VsYXIgdG9vbHRpcHNcclxuJGZvcm0tZmVlZGJhY2stdG9vbHRpcC1wYWRkaW5nLXk6ICR0b29sdGlwLXBhZGRpbmcteTtcclxuJGZvcm0tZmVlZGJhY2stdG9vbHRpcC1wYWRkaW5nLXg6ICR0b29sdGlwLXBhZGRpbmcteDtcclxuJGZvcm0tZmVlZGJhY2stdG9vbHRpcC1mb250LXNpemU6ICR0b29sdGlwLWZvbnQtc2l6ZTtcclxuJGZvcm0tZmVlZGJhY2stdG9vbHRpcC1saW5lLWhlaWdodDogJGxpbmUtaGVpZ2h0LWJhc2U7XHJcbiRmb3JtLWZlZWRiYWNrLXRvb2x0aXAtb3BhY2l0eTogJHRvb2x0aXAtb3BhY2l0eTtcclxuJGZvcm0tZmVlZGJhY2stdG9vbHRpcC1ib3JkZXItcmFkaXVzOiAkdG9vbHRpcC1ib3JkZXItcmFkaXVzO1xyXG5cclxuLy8gUG9wb3ZlcnNcclxuXHJcbiRwb3BvdmVyLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1zbTtcclxuJHBvcG92ZXItYmc6ICR3aGl0ZTtcclxuJHBvcG92ZXItbWF4LXdpZHRoOiAyNzZweDtcclxuJHBvcG92ZXItYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xyXG4kcG9wb3Zlci1ib3JkZXItY29sb3I6IHJnYmEoJGJsYWNrLCAwLjIpO1xyXG4kcG9wb3Zlci1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cy1sZztcclxuJHBvcG92ZXItaW5uZXItYm9yZGVyLXJhZGl1czogc3VidHJhY3QoJHBvcG92ZXItYm9yZGVyLXJhZGl1cywgJHBvcG92ZXItYm9yZGVyLXdpZHRoKTtcclxuJHBvcG92ZXItYm94LXNoYWRvdzogJGJveC1zaGFkb3c7XHJcblxyXG4kcG9wb3Zlci1oZWFkZXItYmc6IGRhcmtlbigkcG9wb3Zlci1iZywgMyUpO1xyXG4kcG9wb3Zlci1oZWFkZXItY29sb3I6ICRoZWFkaW5ncy1jb2xvcjtcclxuJHBvcG92ZXItaGVhZGVyLXBhZGRpbmcteTogMC41cmVtO1xyXG4kcG9wb3Zlci1oZWFkZXItcGFkZGluZy14OiAwLjc1cmVtO1xyXG5cclxuJHBvcG92ZXItYm9keS1jb2xvcjogJGJvZHktY29sb3I7XHJcbiRwb3BvdmVyLWJvZHktcGFkZGluZy15OiAkc3BhY2VyO1xyXG4kcG9wb3Zlci1ib2R5LXBhZGRpbmcteDogJHNwYWNlcjtcclxuXHJcbiRwb3BvdmVyLWFycm93LXdpZHRoOiAxcmVtO1xyXG4kcG9wb3Zlci1hcnJvdy1oZWlnaHQ6IDAuNXJlbTtcclxuJHBvcG92ZXItYXJyb3ctY29sb3I6ICRwb3BvdmVyLWJnO1xyXG5cclxuJHBvcG92ZXItYXJyb3ctb3V0ZXItY29sb3I6IGZhZGUtaW4oJHBvcG92ZXItYm9yZGVyLWNvbG9yLCAwLjA1KTtcclxuXHJcbi8vIFRvYXN0c1xyXG5cclxuJHRvYXN0LW1heC13aWR0aDogMzUwcHg7XHJcbiR0b2FzdC1wYWRkaW5nLXg6IDAuNzVyZW07XHJcbiR0b2FzdC1wYWRkaW5nLXk6IDAuMjVyZW07XHJcbiR0b2FzdC1mb250LXNpemU6IDAuODc1cmVtO1xyXG4kdG9hc3QtY29sb3I6IG51bGw7XHJcbiR0b2FzdC1iYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKCR3aGl0ZSwgMC44NSk7XHJcbiR0b2FzdC1ib3JkZXItd2lkdGg6IDFweDtcclxuJHRvYXN0LWJvcmRlci1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4kdG9hc3QtYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcclxuJHRvYXN0LWJveC1zaGFkb3c6IDAgMC4yNXJlbSAwLjc1cmVtIHJnYmEoJGJsYWNrLCAwLjEpO1xyXG4kdG9hc3Qtc3BhY2luZzogJGNvbnRhaW5lci1wYWRkaW5nLXg7XHJcblxyXG4kdG9hc3QtaGVhZGVyLWNvbG9yOiAkZ3JheS02MDA7XHJcbiR0b2FzdC1oZWFkZXItYmFja2dyb3VuZC1jb2xvcjogcmdiYSgkd2hpdGUsIDAuODUpO1xyXG4kdG9hc3QtaGVhZGVyLWJvcmRlci1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjA1KTtcclxuXHJcbi8vIEJhZGdlc1xyXG4kYmFkZ2UtZm9udC1zaXplOiAwLjc1ZW07XHJcbiRiYWRnZS1mb250LXdlaWdodDogNTAwO1xyXG4kYmFkZ2UtY29sb3I6ICR3aGl0ZTtcclxuJGJhZGdlLXBhZGRpbmcteTogMC4zNWVtO1xyXG4kYmFkZ2UtcGFkZGluZy14OiAwLjVlbTtcclxuJGJhZGdlLWJvcmRlci1yYWRpdXM6IDJweDtcclxuXHJcbi8vIE1vZGFsc1xyXG5cclxuLy8gUGFkZGluZyBhcHBsaWVkIHRvIHRoZSBtb2RhbCBib2R5XHJcbiRtb2RhbC1pbm5lci1wYWRkaW5nOiAxLjI1cmVtO1xyXG5cclxuLy8gTWFyZ2luIGJldHdlZW4gZWxlbWVudHMgaW4gZm9vdGVyLCBtdXN0IGJlIGxvd2VyIHRoYW4gb3IgZXF1YWwgdG8gMiAqICRtb2RhbC1pbm5lci1wYWRkaW5nXHJcbiRtb2RhbC1mb290ZXItbWFyZ2luLWJldHdlZW46IDAuNXJlbTtcclxuJG1vZGFsLWRpYWxvZy1tYXJnaW46IDAuNXJlbTtcclxuJG1vZGFsLWRpYWxvZy1tYXJnaW4teS1zbS11cDogMS43NXJlbTtcclxuXHJcbiRtb2RhbC10aXRsZS1saW5lLWhlaWdodDogJGxpbmUtaGVpZ2h0LWJhc2U7XHJcblxyXG4kbW9kYWwtY29udGVudC1jb2xvcjogbnVsbDtcclxuJG1vZGFsLWNvbnRlbnQtYmc6ICR3aGl0ZTtcclxuJG1vZGFsLWNvbnRlbnQtYm9yZGVyLWNvbG9yOiByZ2JhKCRibGFjaywgMC4yKTtcclxuJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xyXG4kbW9kYWwtY29udGVudC1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cy1sZztcclxuJG1vZGFsLWNvbnRlbnQtaW5uZXItYm9yZGVyLXJhZGl1czogc3VidHJhY3QoJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXJhZGl1cywgJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXdpZHRoKTtcclxuJG1vZGFsLWNvbnRlbnQtYm94LXNoYWRvdy14czogMCAwLjI1cmVtIDAuNXJlbSByZ2JhKCRibGFjaywgMC41KTtcclxuJG1vZGFsLWNvbnRlbnQtYm94LXNoYWRvdy1zbS11cDogMCAwLjVyZW0gMXJlbSByZ2JhKCRibGFjaywgMC41KTtcclxuXHJcbiRtb2RhbC1iYWNrZHJvcC1iZzogJGJsYWNrO1xyXG4kbW9kYWwtYmFja2Ryb3Atb3BhY2l0eTogMC41O1xyXG4kbW9kYWwtaGVhZGVyLWJvcmRlci1jb2xvcjogJGJvcmRlci1jb2xvcjtcclxuJG1vZGFsLWZvb3Rlci1ib3JkZXItY29sb3I6ICRtb2RhbC1oZWFkZXItYm9yZGVyLWNvbG9yO1xyXG4kbW9kYWwtaGVhZGVyLWJvcmRlci13aWR0aDogJG1vZGFsLWNvbnRlbnQtYm9yZGVyLXdpZHRoO1xyXG4kbW9kYWwtZm9vdGVyLWJvcmRlci13aWR0aDogJG1vZGFsLWhlYWRlci1ib3JkZXItd2lkdGg7XHJcbiRtb2RhbC1oZWFkZXItcGFkZGluZy15OiAxLjI1cmVtO1xyXG4kbW9kYWwtaGVhZGVyLXBhZGRpbmcteDogMS41NjI1cmVtO1xyXG4kbW9kYWwtaGVhZGVyLXBhZGRpbmc6ICRtb2RhbC1oZWFkZXItcGFkZGluZy15ICRtb2RhbC1oZWFkZXItcGFkZGluZy14O1xyXG5cclxuJG1vZGFsLWZvb3Rlci1iZzogbnVsbDtcclxuJG1vZGFsLWZvb3Rlci1ib3JkZXItY29sb3I6ICRtb2RhbC1oZWFkZXItYm9yZGVyLWNvbG9yO1xyXG4kbW9kYWwtZm9vdGVyLWJvcmRlci13aWR0aDogJG1vZGFsLWhlYWRlci1ib3JkZXItd2lkdGg7XHJcblxyXG4kbW9kYWwtc206IDMwMHB4O1xyXG4kbW9kYWwtbWQ6IDUwMHB4O1xyXG4kbW9kYWwtbGc6IDgwMHB4O1xyXG4kbW9kYWwteGw6IDExNDBweDtcclxuXHJcbiRtb2RhbC1mYWRlLXRyYW5zZm9ybTogdHJhbnNsYXRlKDAsIC01MHB4KTtcclxuJG1vZGFsLXNob3ctdHJhbnNmb3JtOiBub25lO1xyXG4kbW9kYWwtdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZS1vdXQ7XHJcbiRtb2RhbC1zY2FsZS10cmFuc2Zvcm06IHNjYWxlKDEuMDIpO1xyXG5cclxuLy8gQWxlcnRzXHJcbi8vXHJcbi8vIERlZmluZSBhbGVydCBjb2xvcnMsIGJvcmRlciByYWRpdXMsIGFuZCBwYWRkaW5nLlxyXG5cclxuJGFsZXJ0LXBhZGRpbmcteTogMC43NXJlbTtcclxuJGFsZXJ0LXBhZGRpbmcteDogMS4yNXJlbTtcclxuJGFsZXJ0LW1hcmdpbi1ib3R0b206IDFyZW07XHJcbiRhbGVydC1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cztcclxuJGFsZXJ0LWxpbmstZm9udC13ZWlnaHQ6ICRmb250LXdlaWdodC1ib2xkO1xyXG4kYWxlcnQtYm9yZGVyLXdpZHRoOiAkYm9yZGVyLXdpZHRoO1xyXG5cclxuJGFsZXJ0LWJnLXNjYWxlOiAtODAlO1xyXG4kYWxlcnQtYm9yZGVyLXNjYWxlOiAtNzAlO1xyXG4kYWxlcnQtY29sb3Itc2NhbGU6IDQwJTtcclxuXHJcbiRhbGVydC1kaXNtaXNzaWJsZS1wYWRkaW5nLXI6ICRhbGVydC1wYWRkaW5nLXggKiAzOyAvLyAzeCBjb3ZlcnMgd2lkdGggb2YgeCBwbHVzIGRlZmF1bHQgcGFkZGluZyBvbiBlaXRoZXIgc2lkZVxyXG5cclxuLy8gUHJvZ3Jlc3MgYmFyc1xyXG5cclxuJHByb2dyZXNzLWhlaWdodDogMXJlbTtcclxuJHByb2dyZXNzLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1iYXNlICogMC43NTtcclxuJHByb2dyZXNzLWJnOiAkYm9keS1iZztcclxuJHByb2dyZXNzLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xyXG4kcHJvZ3Jlc3MtYm94LXNoYWRvdzogaW5zZXQgMCAwLjFyZW0gMC4xcmVtIHJnYmEoJGJsYWNrLCAwLjEpO1xyXG4kcHJvZ3Jlc3MtYmFyLWNvbG9yOiAkd2hpdGU7XHJcbiRwcm9ncmVzcy1iYXItYmc6ICRwcmltYXJ5O1xyXG4kcHJvZ3Jlc3MtYmFyLWFuaW1hdGlvbi10aW1pbmc6IDFzIGxpbmVhciBpbmZpbml0ZTtcclxuJHByb2dyZXNzLWJhci10cmFuc2l0aW9uOiB3aWR0aCAwLjZzIGVhc2U7XHJcblxyXG4vLyBMaXN0IGdyb3VwXHJcblxyXG4kbGlzdC1ncm91cC1jb2xvcjogbnVsbDtcclxuJGxpc3QtZ3JvdXAtYmc6ICR3aGl0ZTtcclxuJGxpc3QtZ3JvdXAtYm9yZGVyLWNvbG9yOiAkYm9yZGVyLWNvbG9yO1xyXG4kbGlzdC1ncm91cC1ib3JkZXItd2lkdGg6ICRib3JkZXItd2lkdGg7XHJcbiRsaXN0LWdyb3VwLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzO1xyXG5cclxuJGxpc3QtZ3JvdXAtaXRlbS1wYWRkaW5nLXk6IGNhbGMoJGNhcmQtc3BhY2VyLXkgLyAxLjUpO1xyXG4kbGlzdC1ncm91cC1pdGVtLXBhZGRpbmcteDogJGNhcmQtc3BhY2VyLXg7XHJcbiRsaXN0LWdyb3VwLWl0ZW0tYmctc2NhbGU6IC04MCU7XHJcbiRsaXN0LWdyb3VwLWl0ZW0tY29sb3Itc2NhbGU6IDQwJTtcclxuXHJcbiRsaXN0LWdyb3VwLWhvdmVyLWJnOiAkZ3JheS0xMDA7XHJcbiRsaXN0LWdyb3VwLWFjdGl2ZS1jb2xvcjogJGNvbXBvbmVudC1hY3RpdmUtY29sb3I7XHJcbiRsaXN0LWdyb3VwLWFjdGl2ZS1iZzogJGNvbXBvbmVudC1hY3RpdmUtYmc7XHJcbiRsaXN0LWdyb3VwLWFjdGl2ZS1ib3JkZXItY29sb3I6ICRsaXN0LWdyb3VwLWFjdGl2ZS1iZztcclxuXHJcbiRsaXN0LWdyb3VwLWRpc2FibGVkLWNvbG9yOiAkZ3JheS0zMDA7XHJcbiRsaXN0LWdyb3VwLWRpc2FibGVkLWJnOiAkbGlzdC1ncm91cC1iZztcclxuXHJcbiRsaXN0LWdyb3VwLWFjdGlvbi1jb2xvcjogJGdyYXktNzAwO1xyXG4kbGlzdC1ncm91cC1hY3Rpb24taG92ZXItY29sb3I6ICRsaXN0LWdyb3VwLWFjdGlvbi1jb2xvcjtcclxuXHJcbiRsaXN0LWdyb3VwLWFjdGlvbi1hY3RpdmUtY29sb3I6ICRib2R5LWNvbG9yO1xyXG4kbGlzdC1ncm91cC1hY3Rpb24tYWN0aXZlLWJnOiAkZ3JheS0yMDA7XHJcblxyXG4vLyBJbWFnZSB0aHVtYm5haWxzXHJcblxyXG4kdGh1bWJuYWlsLXBhZGRpbmc6IDAuMjVyZW07XHJcbiR0aHVtYm5haWwtYmc6ICRib2R5LWJnO1xyXG4kdGh1bWJuYWlsLWJvcmRlci13aWR0aDogJGJvcmRlci13aWR0aDtcclxuJHRodW1ibmFpbC1ib3JkZXItY29sb3I6ICRib3JkZXItY29sb3I7XHJcbiR0aHVtYm5haWwtYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXM7XHJcbiR0aHVtYm5haWwtYm94LXNoYWRvdzogMCAxcHggMnB4IHJnYmEoJGJsYWNrLCAwLjA3NSk7XHJcblxyXG4vLyBGaWd1cmVzXHJcblxyXG4kZmlndXJlLWNhcHRpb24tZm9udC1zaXplOiA5MCU7XHJcbiRmaWd1cmUtY2FwdGlvbi1jb2xvcjogJGdyYXktNjAwO1xyXG5cclxuLy8gQnJlYWRjcnVtYnNcclxuXHJcbiRicmVhZGNydW1iLWZvbnQtc2l6ZTogbnVsbDtcclxuJGJyZWFkY3J1bWItcGFkZGluZy15OiAyO1xyXG4kYnJlYWRjcnVtYi1wYWRkaW5nLXg6IDA7XHJcbiRicmVhZGNydW1iLWl0ZW0tcGFkZGluZzogMC41cmVtO1xyXG5cclxuJGJyZWFkY3J1bWItbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuXHJcbiRicmVhZGNydW1iLWJnOiBudWxsO1xyXG4kYnJlYWRjcnVtYi1kaXZpZGVyLWNvbG9yOiAkZ3JheS02MDA7XHJcbiRicmVhZGNydW1iLWFjdGl2ZS1jb2xvcjogJGdyYXktNjAwO1xyXG4kYnJlYWRjcnVtYi1kaXZpZGVyOiB1cmwoXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNDc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nMCAwIDI0IDI0JyB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIHN0cm9rZT0nI3skZ3JheS02MDB9JyBzdHJva2Utd2lkdGg9JzInIGZpbGw9J25vbmUnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgY2xhc3M9J2Nzcy1pNmR6cTEnJTNFJTNDcG9seWxpbmUgcG9pbnRzPSc5IDE4IDE1IDEyIDkgNiclM0UlM0MvcG9seWxpbmUlM0UlM0Mvc3ZnJTNFXCIpO1xyXG4kYnJlYWRjcnVtYi1kaXZpZGVyLWZsaXBwZWQ6ICRicmVhZGNydW1iLWRpdmlkZXI7XHJcbiRicmVhZGNydW1iLWJvcmRlci1yYWRpdXM6IG51bGw7XHJcblxyXG4vLyBDYXJvdXNlbFxyXG5cclxuJGNhcm91c2VsLWNvbnRyb2wtY29sb3I6ICR3aGl0ZTtcclxuJGNhcm91c2VsLWNvbnRyb2wtd2lkdGg6IDE1JTtcclxuJGNhcm91c2VsLWNvbnRyb2wtb3BhY2l0eTogMC41O1xyXG4kY2Fyb3VzZWwtY29udHJvbC1ob3Zlci1vcGFjaXR5OiAwLjk7XHJcbiRjYXJvdXNlbC1jb250cm9sLXRyYW5zaXRpb246IG9wYWNpdHkgMC4xNXMgZWFzZTtcclxuXHJcbiRjYXJvdXNlbC1pbmRpY2F0b3Itd2lkdGg6IDMwcHg7XHJcbiRjYXJvdXNlbC1pbmRpY2F0b3ItaGVpZ2h0OiAzcHg7XHJcbiRjYXJvdXNlbC1pbmRpY2F0b3ItaGl0LWFyZWEtaGVpZ2h0OiAxMHB4O1xyXG4kY2Fyb3VzZWwtaW5kaWNhdG9yLXNwYWNlcjogM3B4O1xyXG4kY2Fyb3VzZWwtaW5kaWNhdG9yLW9wYWNpdHk6IDAuNTtcclxuJGNhcm91c2VsLWluZGljYXRvci1hY3RpdmUtYmc6ICR3aGl0ZTtcclxuJGNhcm91c2VsLWluZGljYXRvci1hY3RpdmUtb3BhY2l0eTogMTtcclxuJGNhcm91c2VsLWluZGljYXRvci10cmFuc2l0aW9uOiBvcGFjaXR5IDAuNnMgZWFzZTtcclxuXHJcbiRjYXJvdXNlbC1jYXB0aW9uLXdpZHRoOiA3MCU7XHJcbiRjYXJvdXNlbC1jYXB0aW9uLWNvbG9yOiAkd2hpdGU7XHJcbiRjYXJvdXNlbC1jYXB0aW9uLXBhZGRpbmcteTogMS4yNXJlbTtcclxuJGNhcm91c2VsLWNhcHRpb24tc3BhY2VyOiAxLjI1cmVtO1xyXG5cclxuJGNhcm91c2VsLWNvbnRyb2wtaWNvbi13aWR0aDogMjBweDtcclxuXHJcbiRjYXJvdXNlbC1jb250cm9sLXByZXYtaWNvbi1iZzogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyBmaWxsPScjeyRjYXJvdXNlbC1jb250cm9sLWNvbG9yfScgdmlld0JveD0nMCAwIDggOCc+PHBhdGggZD0nTTUuMjUgMGwtNCA0IDQgNCAxLjUtMS41TDQuMjUgNGwyLjUtMi41TDUuMjUgMHonLz48L3N2Zz5cIik7XHJcbiRjYXJvdXNlbC1jb250cm9sLW5leHQtaWNvbi1iZzogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyBmaWxsPScjeyRjYXJvdXNlbC1jb250cm9sLWNvbG9yfScgdmlld0JveD0nMCAwIDggOCc+PHBhdGggZD0nTTIuNzUgMGwtMS41IDEuNUwzLjc1IDRsLTIuNSAyLjVMMi43NSA4bDQtNC00LTR6Jy8+PC9zdmc+XCIpO1xyXG5cclxuJGNhcm91c2VsLXRyYW5zaXRpb24tZHVyYXRpb246IDAuNnM7XHJcbiRjYXJvdXNlbC10cmFuc2l0aW9uOiB0cmFuc2Zvcm0gJGNhcm91c2VsLXRyYW5zaXRpb24tZHVyYXRpb24gZWFzZS1pbi1vdXQ7IC8vIERlZmluZSB0cmFuc2Zvcm0gdHJhbnNpdGlvbiBmaXJzdCBpZiB1c2luZyBtdWx0aXBsZSB0cmFuc2l0aW9ucyAoZS5nLiwgYHRyYW5zZm9ybSAycyBlYXNlLCBvcGFjaXR5IC41cyBlYXNlLW91dGApXHJcblxyXG4kY2Fyb3VzZWwtZGFyay1pbmRpY2F0b3ItYWN0aXZlLWJnOiAkYmxhY2s7XHJcbiRjYXJvdXNlbC1kYXJrLWNhcHRpb24tY29sb3I6ICRibGFjaztcclxuJGNhcm91c2VsLWRhcmstY29udHJvbC1pY29uLWZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDApO1xyXG5cclxuLy8gU3Bpbm5lcnNcclxuXHJcbiRzcGlubmVyLXdpZHRoOiAycmVtO1xyXG4kc3Bpbm5lci1oZWlnaHQ6ICRzcGlubmVyLXdpZHRoO1xyXG4kc3Bpbm5lci12ZXJ0aWNhbC1hbGlnbjogLTAuMTI1ZW07XHJcbiRzcGlubmVyLWJvcmRlci13aWR0aDogMC4yNWVtO1xyXG4kc3Bpbm5lci1hbmltYXRpb24tc3BlZWQ6IDAuNzVzO1xyXG5cclxuJHNwaW5uZXItd2lkdGgtc206IDFyZW07XHJcbiRzcGlubmVyLWhlaWdodC1zbTogJHNwaW5uZXItd2lkdGgtc207XHJcbiRzcGlubmVyLWJvcmRlci13aWR0aC1zbTogMC4yZW07XHJcblxyXG4vLyBDbG9zZVxyXG5cclxuJGJ0bi1jbG9zZS13aWR0aDogMWVtO1xyXG4kYnRuLWNsb3NlLWhlaWdodDogJGJ0bi1jbG9zZS13aWR0aDtcclxuJGJ0bi1jbG9zZS1wYWRkaW5nLXg6IDAuMjVlbTtcclxuJGJ0bi1jbG9zZS1wYWRkaW5nLXk6ICRidG4tY2xvc2UtcGFkZGluZy14O1xyXG4kYnRuLWNsb3NlLWNvbG9yOiAkYmxhY2s7XHJcbiRidG4tY2xvc2UtYmc6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCw8c3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nI3skYnRuLWNsb3NlLWNvbG9yfScgdmlld0JveD0nMCAwIDE2IDE2Jz48cGF0aCBkPSdNLjI5My4yOTNhMSAxIDAgMDExLjQxNCAwTDggNi41ODYgMTQuMjkzLjI5M2ExIDEgMCAxMTEuNDE0IDEuNDE0TDkuNDE0IDhsNi4yOTMgNi4yOTNhMSAxIDAgMDEtMS40MTQgMS40MTRMOCA5LjQxNGwtNi4yOTMgNi4yOTNhMSAxIDAgMDEtMS40MTQtMS40MTRMNi41ODYgOCAuMjkzIDEuNzA3YTEgMSAwIDAxMC0xLjQxNHonLz48L3N2Zz5cIik7XHJcbiRidG4tY2xvc2UtZm9jdXMtc2hhZG93OiAkaW5wdXQtYnRuLWZvY3VzLWJveC1zaGFkb3c7XHJcbiRidG4tY2xvc2Utb3BhY2l0eTogMC41O1xyXG4kYnRuLWNsb3NlLWhvdmVyLW9wYWNpdHk6IDAuNzU7XHJcbiRidG4tY2xvc2UtZm9jdXMtb3BhY2l0eTogMTtcclxuJGJ0bi1jbG9zZS1kaXNhYmxlZC1vcGFjaXR5OiAwLjI1O1xyXG4kYnRuLWNsb3NlLXdoaXRlLWZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDAlKSBicmlnaHRuZXNzKDIwMCUpO1xyXG5cclxuLy8gT2ZmY2FudmFzXHJcblxyXG4vLyBzY3NzLWRvY3Mtc3RhcnQgb2ZmY2FudmFzLXZhcmlhYmxlc1xyXG4kb2ZmY2FudmFzLXBhZGRpbmcteTogJG1vZGFsLWlubmVyLXBhZGRpbmc7XHJcbiRvZmZjYW52YXMtcGFkZGluZy14OiAkbW9kYWwtaW5uZXItcGFkZGluZztcclxuJG9mZmNhbnZhcy1ob3Jpem9udGFsLXdpZHRoOiAzNjBweDtcclxuJG9mZmNhbnZhcy12ZXJ0aWNhbC1oZWlnaHQ6IDMzdmg7XHJcbiRvZmZjYW52YXMtdHJhbnNpdGlvbi1kdXJhdGlvbjogMC4zcztcclxuJG9mZmNhbnZhcy1ib3JkZXItY29sb3I6ICRtb2RhbC1jb250ZW50LWJvcmRlci1jb2xvcjtcclxuJG9mZmNhbnZhcy1ib3JkZXItd2lkdGg6ICRtb2RhbC1jb250ZW50LWJvcmRlci13aWR0aDtcclxuJG9mZmNhbnZhcy10aXRsZS1saW5lLWhlaWdodDogJG1vZGFsLXRpdGxlLWxpbmUtaGVpZ2h0O1xyXG4kb2ZmY2FudmFzLWJnLWNvbG9yOiAkbW9kYWwtY29udGVudC1iZztcclxuJG9mZmNhbnZhcy1jb2xvcjogJG1vZGFsLWNvbnRlbnQtY29sb3I7XHJcbiRvZmZjYW52YXMtYm94LXNoYWRvdzogJG1vZGFsLWNvbnRlbnQtYm94LXNoYWRvdy14cztcclxuJG9mZmNhbnZhcy1iYWNrZHJvcC1iZzogJG1vZGFsLWJhY2tkcm9wLWJnO1xyXG4kb2ZmY2FudmFzLWJhY2tkcm9wLW9wYWNpdHk6ICRtb2RhbC1iYWNrZHJvcC1vcGFjaXR5O1xyXG4vLyBzY3NzLWRvY3MtZW5kIG9mZmNhbnZhcy12YXJpYWJsZXNcclxuXHJcbi8vIENvZGVcclxuXHJcbiRjb2RlLWZvbnQtc2l6ZTogJHNtYWxsLWZvbnQtc2l6ZTtcclxuJGNvZGUtY29sb3I6ICRwaW5rO1xyXG5cclxuJGtiZC1wYWRkaW5nLXk6IDAuMTg3NXJlbTtcclxuJGtiZC1wYWRkaW5nLXg6IDAuMzc1cmVtO1xyXG4ka2JkLWZvbnQtc2l6ZTogJGNvZGUtZm9udC1zaXplO1xyXG4ka2JkLWNvbG9yOiB2YXIoLS0jeyRwcmVmaXh9Ym9keS1iZyk7XHJcbiRrYmQtYmc6IHZhcigtLSN7JHByZWZpeH1ib2R5LWNvbG9yKTtcclxuJG5lc3RlZC1rYmQtZm9udC13ZWlnaHQ6IG51bGw7IC8vIERlcHJlY2F0ZWQgaW4gdjUuMi4wLCByZW1vdmluZyBpbiB2NlxyXG5cclxuJHByZS1jb2xvcjogbnVsbDtcclxuIiwiLyoqPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuVGVtcGxhdGUgTmFtZTogQmVycnkgLSBCb290c3RyYXAgQWRtaW4gVGVtcGxhdGVcbkF1dGhvcjogY29kZWR0aGVtZXNcblN1cHBvcnQ6IGh0dHBzOi8vY29kZWR0aGVtZXMuYXV0aG9yZGVzay5hcHBcbkZpbGU6IHN0eWxlLmNzc1xuPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gKi9cbmJvZHkge1xuICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IFwic2FsdFwiO1xufVxuXG46cm9vdCB7XG4gIC0tYnMtYm9keS1iZzogI2VjZWZmMTtcbiAgLS1icy1ib2R5LWJnLXJnYjogMjM2LCAyMzksIDI0MTtcbiAgLS1wYy1oZWFkaW5nLWNvbG9yOiAjMzQzYTQwO1xuICAtLXBjLWFjdGl2ZS1iYWNrZ3JvdW5kOiAjZTllY2VmO1xuICAtLXBjLXNpZGViYXItYmFja2dyb3VuZDogI2ZmZjtcbiAgLS1wYy1zaWRlYmFyLWNvbG9yOiAjNjE2MTYxO1xuICAtLXBjLXNpZGViYXItY29sb3ItcmdiOiA1NywgNzAsIDk1O1xuICAtLXBjLXNpZGViYXItc3VibWVudS1ib3JkZXItY29sb3I6IHZhcigtLWJzLWdyYXktMzAwKTtcbiAgLS1wYy1zaWRlYmFyLWFjdGl2ZS1jb2xvcjogIzY2MTBmMjtcbiAgLS1wYy1zaWRlYmFyLWFjdGl2ZS1jb2xvci1yZ2I6IDEwMiwgMTYsIDI0MjtcbiAgLS1wYy1zaWRlYmFyLXNoYWRvdzogbm9uZTtcbiAgLS1wYy1zaWRlYmFyLWNhcHRpb24tY29sb3I6ICMyMTIxMjE7XG4gIC0tcGMtc2lkZWJhci1ib3JkZXI6IG5vbmU7XG4gIC0tcGMtaGVhZGVyLWJhY2tncm91bmQ6ICNmZmY7XG4gIC0tcGMtaGVhZGVyLWNvbG9yOiAjNjE2MTYxO1xuICAtLXBjLWhlYWRlci1zaGFkb3c6IG5vbmU7XG4gIC0tcGMtY2FyZC1ib3gtc2hhZG93OiBub25lO1xuICAtLXBjLWhlYWRlci1zdWJtZW51LWJhY2tncm91bmQ6ICNmZmZmZmY7XG4gIC0tcGMtaGVhZGVyLXN1Ym1lbnUtY29sb3I6ICMxMTE5MzY7XG59XG5cbltkYXRhLXBjLXNpZGViYXItdGhlbWU9ZGFya10ge1xuICAtLXBjLXNpZGViYXItYmFja2dyb3VuZDogIzFkMjYzMDtcbiAgLS1wYy1zaWRlYmFyLWNvbG9yOiAjZmZmZmZmO1xuICAtLXBjLXNpZGViYXItY29sb3ItcmdiOiAyNTUsIDI1NSwgMjU1O1xuICAtLXBjLXNpZGViYXItc3VibWVudS1ib3JkZXItY29sb3I6IHZhcigtLWJzLWdyYXktNjAwKTtcbiAgLS1wYy1zaWRlYmFyLWNhcHRpb24tY29sb3I6ICM3NDg4OTI7XG59XG5cbnNlY3Rpb24ge1xuICBwYWRkaW5nOiAxMDBweCAwO1xufVxuXG4udGl0bGUge1xuICBtYXJnaW4tYm90dG9tOiA1MHB4O1xufVxuLnRpdGxlIGgyIHtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbn1cblxuLmxhbmRpbmctcGFnZSB7XG4gIG92ZXJmbG93LXg6IGhpZGRlbjtcbn1cbkBtZWRpYSAobWluLXdpZHRoOiAxNjAwcHgpIHtcbiAgLmxhbmRpbmctcGFnZSAuY29udGFpbmVyIHtcbiAgICBtYXgtd2lkdGg6IDEyMDBweDtcbiAgfVxufVxuXG4ubmF2YmFyIHtcbiAgcG9zaXRpb246IGZpeGVkO1xuICBwYWRkaW5nOiAxNnB4IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7XG4gIHotaW5kZXg6IDk5O1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNXB4KTtcbn1cbi5uYXZiYXIudG9wLW5hdi1jb2xsYXBzZSB7XG4gIGJveC1zaGFkb3c6IDAgOHB4IDZweCAtMTBweCByZ2JhKDAsIDAsIDAsIDAuNSk7XG59XG4ubmF2YmFyLmRlZmF1bHQge1xuICB0b3A6IDA7XG4gIGJveC1zaGFkb3c6IG5vbmU7XG59XG4ubmF2YmFyIC5uYXYtbGluayB7XG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG5cbmhlYWRlciB7XG4gIHBhZGRpbmc6IDEwMHB4IDA7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMzYwZGVnLCByZ2IoMjM4LCAyNDIsIDI0NikgMS4wOSUsIHJnYigyNTUsIDI1NSwgMjU1KSAxMDAlKTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuaGVhZGVyOmFmdGVyIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBib3R0b206IDA7XG4gIGJhY2tncm91bmQtaW1hZ2U6IHVybChcIi4uL2ltYWdlcy9sYW5kaW5nL2ltZy1oZWFkZXItYmcucG5nXCIpO1xuICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiByaWdodCBib3R0b207XG4gIGJhY2tncm91bmQtc2l6ZTogODAlO1xuICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xuICB6LWluZGV4OiAxO1xufVxuaGVhZGVyID4gKiB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogNTtcbn1cbmhlYWRlciAuaGVyby1pbWFnZSB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgdHJhbnNmb3JtLW9yaWdpbjogdG9wIGxlZnQ7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS42KTtcbn1cbmhlYWRlciAuaGVyby1pbWFnZSAuaW1nLWJnLWdyaWQge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJvdHRvbTogLTIwcHg7XG4gIHJpZ2h0OiAxNDBweDtcbiAgd2lkdGg6IDcwJTtcbiAgei1pbmRleDogLTE7XG59XG5oZWFkZXIgLmhlcm8taW1hZ2UgLmltZy13aWRnZXQtMSxcbmhlYWRlciAuaGVyby1pbWFnZSAuaW1nLXdpZGdldC0yIHtcbiAgZmlsdGVyOiBkcm9wLXNoYWRvdyhyZ2JhKDMzLCAxNTAsIDI0MywgMC4zKSAwcHggMHB4IDUwcHgpO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbn1cbmhlYWRlciAuaGVyby1pbWFnZSAuaW1nLXdpZGdldC0xIHtcbiAgYW5pbWF0aW9uOiAxMHMgbGluZWFyIDJzIGluZmluaXRlIG5vcm1hbCBub25lIHJ1bm5pbmcgc2xpZGVZO1xufVxuaGVhZGVyIC5oZXJvLWltYWdlIC5pbWctd2lkZ2V0LTIge1xuICBhbmltYXRpb246IDEwcyBsaW5lYXIgMHMgaW5maW5pdGUgbm9ybWFsIG5vbmUgcnVubmluZyBzbGlkZVk7XG59XG5oZWFkZXIgaDEge1xuICBmb250LXNpemU6IDY0cHg7XG59XG5cbi5mZWF0dXJlLWNhcmQge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG4uZmVhdHVyZS1jYXJkOjphZnRlciwgLmZlYXR1cmUtY2FyZDo6YmVmb3JlIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB3aWR0aDogMTUwcHg7XG4gIGhlaWdodDogMTUwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgei1pbmRleDogMTtcbn1cbi5mZWF0dXJlLWNhcmQ6OmJlZm9yZSB7XG4gIGJvcmRlcjogMnB4IHNvbGlkIHJnYigyNTUsIDI1NSwgMjU1KTtcbiAgb3BhY2l0eTogMC4yMTtcbiAgdG9wOiAtOTdweDtcbiAgcmlnaHQ6IC0zcHg7XG59XG4uZmVhdHVyZS1jYXJkOjphZnRlciB7XG4gIGJvcmRlcjogMzVweCBzb2xpZCByZ2IoMjU1LCAyNTUsIDI1NSk7XG4gIG9wYWNpdHk6IDAuNDtcbiAgdG9wOiAtNzJweDtcbiAgcmlnaHQ6IC02M3B4O1xufVxuLmZlYXR1cmUtY2FyZCA+ICoge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHotaW5kZXg6IDU7XG59XG4uZmVhdHVyZS1jYXJkIC5hdnRhciB7XG4gIGJhY2tncm91bmQ6IHJnYigyNTUsIDI1NSwgMjU1KTtcbiAgb3BhY2l0eTogMC41O1xufVxuLmZlYXR1cmUtY2FyZCAuYXZ0YXIgaW1nIHtcbiAgd2lkdGg6IDM2cHg7XG59XG5cbi5jdXN0b21pemUtbGlzdCBsaSB7XG4gIHBhZGRpbmc6IDEwcHggMHB4O1xuICBmb250LXNpemU6IDFyZW07XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC43cmVtO1xufVxuXG4uYXBwbGljYXRpb24tc2xpZGVyIHtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cbi5hcHBsaWNhdGlvbi1zbGlkZXIuYmctcHJpbWFyeSB7XG4gIC0tYnMtYmctb3BhY2l0eTogMC4wNztcbn1cblxuLm93bC10aGVtZSAub3dsLWRvdHMge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIC13ZWJraXQtdGFwLWhpZ2hsaWdodC1jb2xvcjogdHJhbnNwYXJlbnQ7XG59XG4ub3dsLXRoZW1lIC5vd2wtZG90cyAub3dsLWRvdCB7XG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgem9vbTogMTtcbn1cbi5vd2wtdGhlbWUgLm93bC1kb3RzIC5vd2wtZG90IHNwYW4ge1xuICB3aWR0aDogMTBweDtcbiAgaGVpZ2h0OiAxMHB4O1xuICBtYXJnaW46IDVweCA3cHg7XG4gIGJhY2tncm91bmQ6IHJnYmEodmFyKC0tYnMtYm9keS1jb2xvci1yZ2IpLCAwLjMpO1xuICBkaXNwbGF5OiBibG9jaztcbiAgLXdlYmtpdC1iYWNrZmFjZS12aXNpYmlsaXR5OiB2aXNpYmxlO1xuICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuMnMgZWFzZTtcbiAgYm9yZGVyLXJhZGl1czogMzBweDtcbn1cbi5vd2wtdGhlbWUgLm93bC1kb3RzIC5vd2wtZG90LmFjdGl2ZSBzcGFuLFxuLm93bC10aGVtZSAub3dsLWRvdHMgLm93bC1kb3Q6aG92ZXIgc3BhbiB7XG4gIGJhY2tncm91bmQ6IHJnYmEodmFyKC0tYnMtYm9keS1jb2xvci1yZ2IpLCAwLjYpO1xufVxuXG4uYXBwLXNsaWRlci5zd2lwZXIge1xuICBvdmVyZmxvdzogdmlzaWJsZTtcbn1cbi5hcHAtc2xpZGVyIC5hdnRhciB7XG4gIHdpZHRoOiA2NXB4O1xuICBoZWlnaHQ6IDY1cHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgZm9udC1zaXplOiAzNnB4O1xuICBib3gtc2hhZG93OiAwcHggMjRweCAzOHB4IHJnYmEoMjgsIDI4LCAyOSwgMC4xKTtcbiAgYmFja2dyb3VuZDogI2ZmZmZmZjtcbiAgY29sb3I6IHZhcigtLWJzLWJvZHktY29sb3IpO1xufVxuLmFwcC1zbGlkZXIgLmF2dGFyOjphZnRlciB7XG4gIGRpc3BsYXk6IG5vbmU7XG59XG4uYXBwLXNsaWRlciAuYXZ0YXIuc3dpcGVyLWJ1dHRvbi1uZXh0IHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGV4KDE1MCUpO1xufVxuLmFwcC1zbGlkZXIgLmF2dGFyLnN3aXBlci1idXR0b24tcHJldiB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRleCgtMTUwJSk7XG59XG4uYXBwLXNsaWRlciBpbWcge1xuICB0cmFuc2Zvcm06IHNjYWxlKDAuOCk7XG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLWJzLWJvcmRlci1yYWRpdXMpO1xuICBtYXJnaW46IDM1cHggMDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XG59XG4uYXBwLXNsaWRlciAuc3dpcGVyLXNsaWRlIHtcbiAgb3BhY2l0eTogMC41O1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlLWluLW91dDtcbn1cbi5hcHAtc2xpZGVyIC5zd2lwZXItc2xpZGUuc3dpcGVyLXNsaWRlLWFjdGl2ZSB7XG4gIG9wYWNpdHk6IDE7XG59XG4uYXBwLXNsaWRlciAuc3dpcGVyLXNsaWRlLnN3aXBlci1zbGlkZS1hY3RpdmUgaW1nIHtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcbiAgYm94LXNoYWRvdzogMHB4IDI0cHggMzhweCByZ2JhKDI4LCAyOCwgMjksIDAuMSk7XG59XG5cbi50ZXN0YW1lbnRzLWNhcmRzIHtcbiAgY29sdW1uLWdhcDogMS4yNXJlbTtcbiAgb3JwaGFuczogMTtcbiAgd2lkb3dzOiAxO1xuICBjb2x1bW4tY291bnQ6IDE7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cbkBtZWRpYSAobWluLXdpZHRoOiA1NzZweCkge1xuICAudGVzdGFtZW50cy1jYXJkcyB7XG4gICAgY29sdW1uLWNvdW50OiAyO1xuICB9XG59XG5AbWVkaWEgKG1pbi13aWR0aDogOTkycHgpIHtcbiAgLnRlc3RhbWVudHMtY2FyZHMge1xuICAgIGNvbHVtbi1jb3VudDogMztcbiAgfVxufVxuQG1lZGlhIChtaW4td2lkdGg6IDEyMDBweCkge1xuICAudGVzdGFtZW50cy1jYXJkcyB7XG4gICAgY29sdW1uLWNvdW50OiA0O1xuICB9XG59XG4udGVzdGFtZW50cy1jYXJkczo6YWZ0ZXIge1xuICBjb250ZW50OiBcIlwiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJvdHRvbTogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgYm90dG9tOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDBkZWcsIHJnYigyNTUsIDI1NSwgMjU1KSwgdHJhbnNwYXJlbnQpO1xuICBoZWlnaHQ6IDUwJTtcbn1cbi50ZXN0YW1lbnRzLWNhcmRzIC5jYXJkIHtcbiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tYnMtYm9yZGVyLWNvbG9yKTtcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICB3aWR0aDogMTAwJTtcbn1cblxuLmNob29zZS1zZWN0aW9uIHtcbiAgcGFkZGluZy1ib3R0b206IDA7XG4gIHBhZGRpbmctdG9wOiAzMHB4O1xufVxuLmNob29zZS1zZWN0aW9uIC5oYW5kLWltZyB7XG4gIHdpZHRoOiAyMjBweDtcbiAgZmlsdGVyOiBkcm9wLXNoYWRvdyhyZ2JhKDMzLCAxNTAsIDI0MywgMC4zKSAwcHggMHB4IDUwcHgpO1xufVxuLmNob29zZS1zZWN0aW9uIGgyIHtcbiAgZm9udC1zaXplOiAyLjVyZW07XG59XG5cbi5jaG9vc2Utc2xpZGVyIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAyMDBweDtcbn1cbi5jaG9vc2Utc2xpZGVyIC5zd2lwZXItc2xpZGUge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGZvbnQtc2l6ZTogMThweDtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBoZWlnaHQ6IDUwcHggIWltcG9ydGFudDtcbiAgb3BhY2l0eTogMC4wODtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XG59XG4uY2hvb3NlLXNsaWRlciAuc3dpcGVyLXNsaWRlIGgyIHtcbiAgbWFyZ2luLWJvdHRvbTogMDtcbiAgY29sb3I6ICNmZmY7XG59XG4uY2hvb3NlLXNsaWRlciAuc3dpcGVyLXNsaWRlLnN3aXBlci1zbGlkZS1wcmV2LCAuY2hvb3NlLXNsaWRlciAuc3dpcGVyLXNsaWRlLnN3aXBlci1zbGlkZS1uZXh0IHtcbiAgb3BhY2l0eTogMC4yO1xufVxuLmNob29zZS1zbGlkZXIgLnN3aXBlci1zbGlkZS5zd2lwZXItc2xpZGUtYWN0aXZlIHtcbiAgb3BhY2l0eTogMTtcbn1cbi5jaG9vc2Utc2xpZGVyIC5zd2lwZXItc2xpZGUuc3dpcGVyLXNsaWRlLWFjdGl2ZSBoMiB7XG4gIGNvbG9yOiB2YXIoLS1icy1wcmltYXJ5KTtcbn1cblxuLmZyYW1ld29ya3MtY2FyZCB7XG4gIGJhY2tncm91bmQ6IHZhcigtLWJzLWJvZHktYmcpO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgbWFyZ2luOiAxMHB4IDAgMzVweDtcbn1cbi5mcmFtZXdvcmtzLWNhcmQ6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiByZ2JhKHZhcigtLWJzLXByaW1hcnktcmdiKSwgMC4wOCk7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggMCByZ2JhKHZhcigtLWJzLXByaW1hcnktcmdiKSwgMC4xNSk7XG59XG4uZnJhbWV3b3Jrcy1jYXJkIC5mcmFtZXdvcmtzLWxvZ28ge1xuICB3aWR0aDogYXV0bztcbiAgaGVpZ2h0OiA0OHB4O1xuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xufVxuLmZyYW1ld29ya3MtY2FyZCAuZnJhbWV3b3Jrcy1iYWRnZSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAxMDAlO1xuICBsZWZ0OiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpO1xuICBmb250LXNpemU6IDAuNzVyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIGJhY2tncm91bmQ6IHZhcigtLWJzLWNhcmQtYmcpO1xuICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1icy1wcmltYXJ5KTtcbiAgY29sb3I6IHZhcigtLWJzLXByaW1hcnkpO1xuICBib3JkZXItcmFkaXVzOiAzMHB4O1xuICBwYWRkaW5nOiAwIDVweDtcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbn1cblxuLmZyYW1ld29ya3Mtc2VjdGlvbiB7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG4uZnJhbWV3b3Jrcy1zZWN0aW9uIC5mcmFtZXdvcmtzLXNsaWRlci5zd2lwZXIge1xuICBvdmVyZmxvdzogdmlzaWJsZTtcbn1cblxuLmZvb3RlciB7XG4gIHBhZGRpbmc6IDEwMHB4IDAgMDtcbn1cbi5mb290ZXIgLnN1Yi1mb290ZXIge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBwYWRkaW5nOiAyMHB4IDBweDtcbiAgbWFyZ2luLXRvcDogMTAwcHg7XG59XG4uZm9vdGVyIC5mb290ZXItbGluayBhIHtcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjFzIGVhc2UtaW4tb3V0O1xuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICBtYXJnaW4tdG9wOiAyMHB4O1xufVxuLmZvb3RlciAuZm9vdGVyLWxpbmsgYSBpIHtcbiAgZm9udC1zaXplOiAyMHB4O1xuICBtYXJnaW4tcmlnaHQ6IDVweDtcbn1cbi5mb290ZXIgLmZvb3Rlci1saW5rIGE6aG92ZXIge1xuICBjb2xvcjogcmdiKDI1NSwgMjU1LCAyNTUpO1xufVxuXG5Aa2V5ZnJhbWVzIHNsaWRlWSB7XG4gIDAlLCA1MCUsIDEwMCUge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcbiAgfVxuICAyNSUge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7XG4gIH1cbiAgNzUlIHtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7XG4gIH1cbn1cbkBrZXlmcmFtZXMgdXBkb3duIHtcbiAgNTAlIHtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTQwcHgpO1xuICB9XG4gIDEwMCUge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcbiAgfVxufVxuQG1lZGlhIChtYXgtd2lkdGg6IDk5MS45OHB4KSB7XG4gIGhlYWRlciB7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG4gIHNlY3Rpb24ge1xuICAgIHBhZGRpbmc6IDQwcHggMDtcbiAgfVxuICAuY2hvb3NlLXNlY3Rpb24gaDIge1xuICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICB9XG59XG5AbWVkaWEgKG1heC13aWR0aDogNzY3Ljk4cHgpIHtcbiAgaGVhZGVyIHtcbiAgICBwYWRkaW5nOiAxMDBweCAwIDUwcHg7XG4gIH1cbiAgaGVhZGVyIGgxIHtcbiAgICBmb250LXNpemU6IDMwcHg7XG4gIH1cbiAgLmFwcC1zbGlkZXIgLmF2dGFyIHtcbiAgICBkaXNwbGF5OiBub25lO1xuICB9XG4gIC5jaG9vc2Utc2VjdGlvbiB7XG4gICAgcGFkZGluZy10b3A6IDMwcHg7XG4gICAgcGFkZGluZy1ib3R0b206IDMwcHg7XG4gIH1cbiAgLmZvb3RlciAuZm9vdGVyLWxpbmsgYSB7XG4gICAgbWFyZ2luLXRvcDogOHB4O1xuICB9XG59XG5AbWVkaWEgKG1heC13aWR0aDogNTc1Ljk4cHgpIHtcbiAgLmNob29zZS1zZWN0aW9uIGgyIHtcbiAgICBmb250LXNpemU6IDEuMXJlbTtcbiAgfVxuICAuY2hvb3NlLXNsaWRlciB7XG4gICAgaGVpZ2h0OiAxMjBweDtcbiAgfVxuICAuY2hvb3NlLXNsaWRlciAuc3dpcGVyLXNsaWRlIHtcbiAgICBoZWlnaHQ6IDMwcHggIWltcG9ydGFudDtcbiAgfVxufVxuW2RhdGEtcGMtZGlyZWN0aW9uPXJ0bF0gaGVhZGVyIC5oZXJvLWltYWdlIHtcbiAgdHJhbnNmb3JtLW9yaWdpbjogdG9wIHJpZ2h0O1xufVxuXG5bZGF0YS1wYy10aGVtZT1kYXJrXSBoZWFkZXIge1xuICBiYWNrZ3JvdW5kOiByZ2IoMTcsIDI1LCA1NCk7XG59XG5bZGF0YS1wYy10aGVtZT1kYXJrXVtkYXRhLXBjLXByZXNldD1wcmVzZXQtMV0gLmFwcC1zbGlkZXIgLmF2dGFyIHtcbiAgYmFja2dyb3VuZDogIzExMTkzNjtcbn1cbltkYXRhLXBjLXRoZW1lPWRhcmtdW2RhdGEtcGMtcHJlc2V0PXByZXNldC0xXSAubmF2YmFyLm5hdmJhci1saWdodCAubmF2YmFyLXRvZ2dsZXItaWNvbiB7XG4gIGZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDAlKSBicmlnaHRuZXNzKDIwMCUpO1xufVxuW2RhdGEtcGMtdGhlbWU9ZGFya11bZGF0YS1wYy1wcmVzZXQ9cHJlc2V0LTFdIC5uYXZiYXIubmF2YmFyLWxpZ2h0Om5vdCguY2FyZCkge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDE3LCAyNSwgNTQsIDAuOCk7XG59XG5bZGF0YS1wYy10aGVtZT1kYXJrXVtkYXRhLXBjLXByZXNldD1wcmVzZXQtMl0gLmFwcC1zbGlkZXIgLmF2dGFyIHtcbiAgYmFja2dyb3VuZDogIzBlMWIyMztcbn1cbltkYXRhLXBjLXRoZW1lPWRhcmtdW2RhdGEtcGMtcHJlc2V0PXByZXNldC0yXSAubmF2YmFyLm5hdmJhci1saWdodCAubmF2YmFyLXRvZ2dsZXItaWNvbiB7XG4gIGZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDAlKSBicmlnaHRuZXNzKDIwMCUpO1xufVxuW2RhdGEtcGMtdGhlbWU9ZGFya11bZGF0YS1wYy1wcmVzZXQ9cHJlc2V0LTJdIC5uYXZiYXIubmF2YmFyLWxpZ2h0Om5vdCguY2FyZCkge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDE0LCAyNywgMzUsIDAuOCk7XG59XG5bZGF0YS1wYy10aGVtZT1kYXJrXVtkYXRhLXBjLXByZXNldD1wcmVzZXQtM10gLmFwcC1zbGlkZXIgLmF2dGFyIHtcbiAgYmFja2dyb3VuZDogIzBhMGYyMztcbn1cbltkYXRhLXBjLXRoZW1lPWRhcmtdW2RhdGEtcGMtcHJlc2V0PXByZXNldC0zXSAubmF2YmFyLm5hdmJhci1saWdodCAubmF2YmFyLXRvZ2dsZXItaWNvbiB7XG4gIGZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDAlKSBicmlnaHRuZXNzKDIwMCUpO1xufVxuW2RhdGEtcGMtdGhlbWU9ZGFya11bZGF0YS1wYy1wcmVzZXQ9cHJlc2V0LTNdIC5uYXZiYXIubmF2YmFyLWxpZ2h0Om5vdCguY2FyZCkge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDEwLCAxNSwgMzUsIDAuOCk7XG59XG5bZGF0YS1wYy10aGVtZT1kYXJrXVtkYXRhLXBjLXByZXNldD1wcmVzZXQtNF0gLmFwcC1zbGlkZXIgLmF2dGFyIHtcbiAgYmFja2dyb3VuZDogIzAxMDYwNjtcbn1cbltkYXRhLXBjLXRoZW1lPWRhcmtdW2RhdGEtcGMtcHJlc2V0PXByZXNldC00XSAubmF2YmFyLm5hdmJhci1saWdodCAubmF2YmFyLXRvZ2dsZXItaWNvbiB7XG4gIGZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDAlKSBicmlnaHRuZXNzKDIwMCUpO1xufVxuW2RhdGEtcGMtdGhlbWU9ZGFya11bZGF0YS1wYy1wcmVzZXQ9cHJlc2V0LTRdIC5uYXZiYXIubmF2YmFyLWxpZ2h0Om5vdCguY2FyZCkge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDEsIDYsIDYsIDAuOCk7XG59XG5bZGF0YS1wYy10aGVtZT1kYXJrXVtkYXRhLXBjLXByZXNldD1wcmVzZXQtNV0gLmFwcC1zbGlkZXIgLmF2dGFyIHtcbiAgYmFja2dyb3VuZDogIzAzMDcwODtcbn1cbltkYXRhLXBjLXRoZW1lPWRhcmtdW2RhdGEtcGMtcHJlc2V0PXByZXNldC01XSAubmF2YmFyLm5hdmJhci1saWdodCAubmF2YmFyLXRvZ2dsZXItaWNvbiB7XG4gIGZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDAlKSBicmlnaHRuZXNzKDIwMCUpO1xufVxuW2RhdGEtcGMtdGhlbWU9ZGFya11bZGF0YS1wYy1wcmVzZXQ9cHJlc2V0LTVdIC5uYXZiYXIubmF2YmFyLWxpZ2h0Om5vdCguY2FyZCkge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDMsIDcsIDgsIDAuOCk7XG59XG5bZGF0YS1wYy10aGVtZT1kYXJrXVtkYXRhLXBjLXByZXNldD1wcmVzZXQtNl0gLmFwcC1zbGlkZXIgLmF2dGFyIHtcbiAgYmFja2dyb3VuZDogIzA1MTMyNztcbn1cbltkYXRhLXBjLXRoZW1lPWRhcmtdW2RhdGEtcGMtcHJlc2V0PXByZXNldC02XSAubmF2YmFyLm5hdmJhci1saWdodCAubmF2YmFyLXRvZ2dsZXItaWNvbiB7XG4gIGZpbHRlcjogaW52ZXJ0KDEpIGdyYXlzY2FsZSgxMDAlKSBicmlnaHRuZXNzKDIwMCUpO1xufVxuW2RhdGEtcGMtdGhlbWU9ZGFya11bZGF0YS1wYy1wcmVzZXQ9cHJlc2V0LTZdIC5uYXZiYXIubmF2YmFyLWxpZ2h0Om5vdCguY2FyZCkge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDUsIDE5LCAzOSwgMC44KTtcbn1cbltkYXRhLXBjLXRoZW1lPWRhcmtdW2RhdGEtcGMtcHJlc2V0PXByZXNldC03XSAuYXBwLXNsaWRlciAuYXZ0YXIge1xuICBiYWNrZ3JvdW5kOiAjMWEyMjNmO1xufVxuW2RhdGEtcGMtdGhlbWU9ZGFya11bZGF0YS1wYy1wcmVzZXQ9cHJlc2V0LTddIC5uYXZiYXIubmF2YmFyLWxpZ2h0IC5uYXZiYXItdG9nZ2xlci1pY29uIHtcbiAgZmlsdGVyOiBpbnZlcnQoMSkgZ3JheXNjYWxlKDEwMCUpIGJyaWdodG5lc3MoMjAwJSk7XG59XG5bZGF0YS1wYy10aGVtZT1kYXJrXVtkYXRhLXBjLXByZXNldD1wcmVzZXQtN10gLm5hdmJhci5uYXZiYXItbGlnaHQ6bm90KC5jYXJkKSB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjYsIDM0LCA2MywgMC44KTtcbn0iLCIvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cclxuLy8gICAgIExpc3Qgb2YgdmFyaWFibGVzIGZvciBsYXlvdXRcclxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XHJcbjpyb290IHtcclxuICAvLyBib2R5XHJcbiAgLS0jeyR2YXJpYWJsZS1wcmVmaXh9Ym9keS1iZzogI3skYm9keS1iZ307XHJcbiAgLS1icy1ib2R5LWJnLXJnYjogI3t0by1yZ2IoJGJvZHktYmcpfTtcclxuXHJcbiAgLS1wYy1oZWFkaW5nLWNvbG9yOiAjeyRncmF5LTgwMH07XHJcbiAgLS1wYy1hY3RpdmUtYmFja2dyb3VuZDogI3skZ3JheS0yMDB9O1xyXG5cclxuICAvLyBOYXZiYXJcclxuICAtLXBjLXNpZGViYXItYmFja2dyb3VuZDogI2ZmZjtcclxuICAtLXBjLXNpZGViYXItY29sb3I6ICM2MTYxNjE7XHJcbiAgLS1wYy1zaWRlYmFyLWNvbG9yLXJnYjogI3t0by1yZ2IoIzM5NDY1Zil9O1xyXG4gIC0tcGMtc2lkZWJhci1zdWJtZW51LWJvcmRlci1jb2xvcjogdmFyKC0tYnMtZ3JheS0zMDApO1xyXG4gIC0tcGMtc2lkZWJhci1hY3RpdmUtY29sb3I6ICN7JHNlY29uZGFyeX07XHJcbiAgLS1wYy1zaWRlYmFyLWFjdGl2ZS1jb2xvci1yZ2I6ICN7dG8tcmdiKCRzZWNvbmRhcnkpfTtcclxuICAtLXBjLXNpZGViYXItc2hhZG93OiBub25lO1xyXG4gIC0tcGMtc2lkZWJhci1jYXB0aW9uLWNvbG9yOiAjMjEyMTIxO1xyXG4gIC0tcGMtc2lkZWJhci1ib3JkZXI6IG5vbmU7XHJcblxyXG4gIC8vIGhlYWRlclxyXG4gIC0tcGMtaGVhZGVyLWJhY2tncm91bmQ6ICNmZmY7XHJcbiAgLS1wYy1oZWFkZXItY29sb3I6ICM2MTYxNjE7XHJcbiAgLS1wYy1oZWFkZXItc2hhZG93OiBub25lO1xyXG5cclxuICAvLyBjYXJkXHJcbiAgLS1wYy1jYXJkLWJveC1zaGFkb3c6IG5vbmU7XHJcblxyXG4gIC8vIGhvcml6b250YWwgbWVudVxyXG4gIC0tcGMtaGVhZGVyLXN1Ym1lbnUtYmFja2dyb3VuZDogI3skd2hpdGV9O1xyXG4gIC0tcGMtaGVhZGVyLXN1Ym1lbnUtY29sb3I6ICN7IzExMTkzNn07XHJcbn1cclxuW2RhdGEtcGMtc2lkZWJhci10aGVtZT0nZGFyayddIHtcclxuICAvLyBOYXZiYXJcclxuICAtLXBjLXNpZGViYXItYmFja2dyb3VuZDogIzFkMjYzMDtcclxuICAtLXBjLXNpZGViYXItY29sb3I6ICNmZmZmZmY7XHJcbiAgLS1wYy1zaWRlYmFyLWNvbG9yLXJnYjogI3t0by1yZ2IoI2ZmZmZmZil9O1xyXG4gIC0tcGMtc2lkZWJhci1zdWJtZW51LWJvcmRlci1jb2xvcjogdmFyKC0tYnMtZ3JheS02MDApO1xyXG4gIC0tcGMtc2lkZWJhci1jYXB0aW9uLWNvbG9yOiAjNzQ4ODkyO1xyXG59XHJcblxyXG4kaGVhZGVyLWhlaWdodDogODBweDtcclxuJHNpZGViYXItd2lkdGg6IDI2MHB4O1xyXG4kc2lkZWJhci1jb2xsYXBzZWQtd2lkdGg6IDgwcHg7XHJcbiRzaWRlYmFyLWNvbGxhcHNlZC1hY3RpdmUtd2lkdGg6IDMwMHB4O1xyXG4kc2lkZWJhci10YWItd2lkdGg6IDc1cHg7XHJcbiRzaWRlYmFyLXRhYi1uYXZiYXItd2lkdGg6IDMyMHB4O1xyXG5cclxuLy8gaG9yaXpvbnRhbCBtZW51XHJcbiR0b3BiYXItaGVpZ2h0OiA2MHB4O1xyXG5cclxuLy8gY2FyZCBibG9ja1xyXG4kY2FyZC1zaGFkb3c6IG5vbmU7XHJcblxyXG4kc29mdC1iZy1sZXZlbDogLTgwJTtcclxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxyXG4vLyAgICAgIFZhcmlhYmxlcyBmb3IgZGFyayBsYXlvdXRzXHJcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cclxuJGRhcmstbGF5b3V0LWNvbG9yOiAkZ3JheS05MDA7XHJcblxyXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XHJcbi8vICAgICAgVmFyaWFibGVzIGZvciBib290c3RyYXAgY29sb3JcclxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxyXG5cclxuJGJsdWU6ICRibHVlLTUwMDtcclxuJHNlY29uZGFyeTogIzM5NDY1ZjtcclxuJGluZGlnbzogJGluZGlnby01MDA7XHJcbiRwdXJwbGU6ICRwdXJwbGUtNTAwO1xyXG4kcGluazogJHBpbmstNTAwO1xyXG4kcmVkOiAkcmVkLTUwMDtcclxuJG9yYW5nZTogJG9yYW5nZS01MDA7XHJcbiR5ZWxsb3c6ICR5ZWxsb3ctNTAwO1xyXG4kZ3JlZW46ICRncmVlbi01MDA7XHJcbiR0ZWFsOiAkdGVhbC01MDA7XHJcbiRjeWFuOiAkY3lhbi01MDA7XHJcblxyXG4kcHJpbWFyeS10ZXh0OiAkYmx1ZS02MDA7XHJcbiRzZWNvbmRhcnktdGV4dDogIzM5NDY1ZjtcclxuJHN1Y2Nlc3MtdGV4dDogJGdyZWVuLTYwMDtcclxuJGluZm8tdGV4dDogJGN5YW4tNzAwO1xyXG4kd2FybmluZy10ZXh0OiAkeWVsbG93LTcwMDtcclxuJGRhbmdlci10ZXh0OiAkcmVkLTYwMDtcclxuJGxpZ2h0LXRleHQ6ICMzOTQ2NWY7XHJcbiRkYXJrLXRleHQ6ICRncmF5LTcwMDtcclxuXHJcbiRwcmltYXJ5LWJnLXN1YnRsZTogJGJsdWUtMTAwO1xyXG4kc2Vjb25kYXJ5LWJnLXN1YnRsZTogJGdyYXktMTAwO1xyXG4kc3VjY2Vzcy1iZy1zdWJ0bGU6ICRncmVlbi0xMDA7XHJcbiRpbmZvLWJnLXN1YnRsZTogJGN5YW4tMTAwO1xyXG4kd2FybmluZy1iZy1zdWJ0bGU6ICR5ZWxsb3ctMTAwO1xyXG4kZGFuZ2VyLWJnLXN1YnRsZTogJHJlZC0xMDA7XHJcbiRsaWdodC1iZy1zdWJ0bGU6IG1peCgkZ3JheS0xMDAsICR3aGl0ZSk7XHJcbiRkYXJrLWJnLXN1YnRsZTogJGdyYXktNDAwO1xyXG5cclxuJHByaW1hcnktYm9yZGVyLXN1YnRsZTogJGJsdWUtMjAwO1xyXG4kc2Vjb25kYXJ5LWJvcmRlci1zdWJ0bGU6ICRncmF5LTIwMDtcclxuJHN1Y2Nlc3MtYm9yZGVyLXN1YnRsZTogJGdyZWVuLTIwMDtcclxuJGluZm8tYm9yZGVyLXN1YnRsZTogJGN5YW4tMjAwO1xyXG4kd2FybmluZy1ib3JkZXItc3VidGxlOiAkeWVsbG93LTIwMDtcclxuJGRhbmdlci1ib3JkZXItc3VidGxlOiAkcmVkLTIwMDtcclxuJGxpZ2h0LWJvcmRlci1zdWJ0bGU6ICRncmF5LTIwMDtcclxuJGRhcmstYm9yZGVyLXN1YnRsZTogJGdyYXktNTAwO1xyXG4kYnJhbmQtY29sb3JzOiAoXHJcbiAgJ2JyYW5kLWNvbG9yLTEnOiAkYnJhbmQtY29sb3IxLFxyXG4gICdicmFuZC1jb2xvci0yJzogJGJyYW5kLWNvbG9yMixcclxuICAnYnJhbmQtY29sb3ItMyc6ICRicmFuZC1jb2xvcjMsXHJcbiAgJ2JyYW5kLWNvbG9yLTQnOiAkYnJhbmQtY29sb3I0XHJcbik7XHJcbiIsIi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxyXG4vLyAgIExpc3Qgb2YgdmFyaWFibGVzIGZvciBQcmVzZXQgY29sb3JcclxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XHJcbiRicmFuZC1jb2xvcjE6IGxpbmVhci1ncmFkaWVudCg4My4zMWRlZywgIzFkZTliNiAyMS4yMiUsICMxZGM0ZTkgODguNTQlKTtcclxuJGJyYW5kLWNvbG9yMjogbGluZWFyLWdyYWRpZW50KDIwNi40OGRlZywgI2EzODlkNCAxMS4xNCUsICM4OTllZDQgMTA0LjYlKTtcclxuJGJyYW5kLWNvbG9yMzogbGluZWFyLWdyYWRpZW50KDIwNy45MmRlZywgIzAzOThmMiAxMS40MiUsICMzOGI5ZTcgMTA2LjU1JSk7XHJcbiRicmFuZC1jb2xvcjQ6IGxpbmVhci1ncmFkaWVudCgxNjIuNTdkZWcsICNjNWVjZjkgMTEuOTUlLCAjYzRiNGU0IDk3LjMyJSk7XHJcblxyXG4kcHJlc2V0LWNvbG9yczogKFxyXG4gIHByZXNldC0xOiAoXHJcbiAgICBib2R5OiAjZWVmMmY2LFxyXG4gICAgcHJpbWFyeTogIzIxOTZmMyxcclxuICAgIHByaW1hcnktZGFyazogIzFjNzZkYSxcclxuICAgIHNlY29uZGFyeTogIzY3M2FiNyxcclxuICAgIHNlY29uZGFyeS1kYXJrOiAjNTQyY2E3XHJcbiAgKSxcclxuICBwcmVzZXQtMjogKFxyXG4gICAgYm9keTogI2VlZjJmNixcclxuICAgIHByaW1hcnk6ICM2MDdkOGIsXHJcbiAgICBwcmltYXJ5LWRhcms6ICM0ZTZhNzgsXHJcbiAgICBzZWNvbmRhcnk6ICMwMDk2ODgsXHJcbiAgICBzZWNvbmRhcnktZGFyazogIzAwODM3NVxyXG4gICksXHJcbiAgcHJlc2V0LTM6IChcclxuICAgIGJvZHk6ICNlZWYyZjYsXHJcbiAgICBwcmltYXJ5OiAjMjAzNDYxLFxyXG4gICAgcHJpbWFyeS1kYXJrOiAjMTgyNzRmLFxyXG4gICAgc2Vjb25kYXJ5OiAjZWM0MDdhLFxyXG4gICAgc2Vjb25kYXJ5LWRhcms6ICNlNzMyNjdcclxuICApLFxyXG4gIHByZXNldC00OiAoXHJcbiAgICBib2R5OiAjZWVmMmY2LFxyXG4gICAgcHJpbWFyeTogIzE2NTk1YSxcclxuICAgIHByaW1hcnktZGFyazogIzEwNDg0OCxcclxuICAgIHNlY29uZGFyeTogI2M3N2UyMyxcclxuICAgIHNlY29uZGFyeS1kYXJrOiAjYmE2YjFhXHJcbiAgKSxcclxuICBwcmVzZXQtNTogKFxyXG4gICAgYm9keTogI2VlZjJmNixcclxuICAgIHByaW1hcnk6ICMxNzNlNDMsXHJcbiAgICBwcmltYXJ5LWRhcms6ICMxMTMwMzQsXHJcbiAgICBzZWNvbmRhcnk6ICMzZmIwYWMsXHJcbiAgICBzZWNvbmRhcnktZGFyazogIzMxYTA5YlxyXG4gICksXHJcbiAgcHJlc2V0LTY6IChcclxuICAgIGJvZHk6ICNlZWYyZjYsXHJcbiAgICBwcmltYXJ5OiAjMGEyMzQyLFxyXG4gICAgcHJpbWFyeS1kYXJrOiAjMDcxYTMzLFxyXG4gICAgc2Vjb25kYXJ5OiAjMmNhNThkLFxyXG4gICAgc2Vjb25kYXJ5LWRhcms6ICMyMTkzN2FcclxuICApLFxyXG4gIHByZXNldC03OiAoXHJcbiAgICBib2R5OiAjZWVmMmY2LFxyXG4gICAgcHJpbWFyeTogIzNmNTFiNSxcclxuICAgIHByaW1hcnktZGFyazogIzMxNDBhNSxcclxuICAgIHNlY29uZGFyeTogIzNmNTFiNSxcclxuICAgIHNlY29uZGFyeS1kYXJrOiAjMzE0MGE1XHJcbiAgKVxyXG4pO1xyXG4kZGFyay1wcmVzZXQtY29sb3JzOiAoXHJcbiAgcHJlc2V0LTE6IChcclxuICAgIGJvZHk6ICMxMTE5MzZcclxuICApLFxyXG4gIHByZXNldC0yOiAoXHJcbiAgICBib2R5OiAjMGUxYjIzXHJcbiAgKSxcclxuICBwcmVzZXQtMzogKFxyXG4gICAgYm9keTogIzBhMGYyM1xyXG4gICksXHJcbiAgcHJlc2V0LTQ6IChcclxuICAgIGJvZHk6ICMwMTA2MDZcclxuICApLFxyXG4gIHByZXNldC01OiAoXHJcbiAgICBib2R5OiAjMDMwNzA4XHJcbiAgKSxcclxuICBwcmVzZXQtNjogKFxyXG4gICAgYm9keTogIzA1MTMyN1xyXG4gICksXHJcbiAgcHJlc2V0LTc6IChcclxuICAgIGJvZHk6ICMxYTIyM2ZcclxuICApXHJcbik7XHJcblxyXG4kYmx1ZS0xMDA6ICNiZWRjZjY7XHJcbiRibHVlLTIwMDogIzkzYzRmMTtcclxuJGJsdWUtMzAwOiAjNjdhY2ViO1xyXG4kYmx1ZS00MDA6ICM0NzliZTY7XHJcbiRibHVlLTUwMDogIzI2ODllMjtcclxuJGJsdWUtNjAwOiAjMjI4MWRmO1xyXG4kYmx1ZS03MDA6ICMxYzc2ZGE7XHJcbiRibHVlLTgwMDogIzE3NmNkNjtcclxuJGJsdWUtOTAwOiAjMGQ1OWNmO1xyXG5cclxuJGluZGlnby0xMDA6ICNkMWI3ZmI7XHJcbiRpbmRpZ28tMjAwOiAjYjM4OGY5O1xyXG4kaW5kaWdvLTMwMDogIzk0NThmNjtcclxuJGluZGlnby00MDA6ICM3ZDM0ZjQ7XHJcbiRpbmRpZ28tNTAwOiAjNjYxMGYyO1xyXG4kaW5kaWdvLTYwMDogIzVlMGVmMDtcclxuJGluZGlnby03MDA6ICM1MzBjZWU7XHJcbiRpbmRpZ28tODAwOiAjNDkwOWVjO1xyXG4kaW5kaWdvLTkwMDogIzM4MDVlODtcclxuXHJcbiRwdXJwbGUtMTAwOiAjZDFjNGU5O1xyXG4kcHVycGxlLTIwMDogI2IzOWRkYjtcclxuJHB1cnBsZS0zMDA6ICM5NTc1Y2Q7XHJcbiRwdXJwbGUtNDAwOiAjN2U1OGMyO1xyXG4kcHVycGxlLTUwMDogIzY3M2FiNztcclxuJHB1cnBsZS02MDA6ICM1ZjM0YjA7XHJcbiRwdXJwbGUtNzAwOiAjNTQyY2E3O1xyXG4kcHVycGxlLTgwMDogIzRhMjU5ZjtcclxuJHB1cnBsZS05MDA6ICMzOTE4OTA7XHJcblxyXG4kcGluay0xMDA6ICNmOGM1ZGQ7XHJcbiRwaW5rLTIwMDogI2Y0OWZjNjtcclxuJHBpbmstMzAwOiAjZWY3OGFmO1xyXG4kcGluay00MDA6ICNlYjViOWQ7XHJcbiRwaW5rLTUwMDogI2U4M2U4YztcclxuJHBpbmstNjAwOiAjZTUzODg0O1xyXG4kcGluay03MDA6ICNlMjMwNzk7XHJcbiRwaW5rLTgwMDogI2RlMjg2ZjtcclxuJHBpbmstOTAwOiAjZDgxYjVjO1xyXG5cclxuJHJlZC0xMDA6ICNmY2M3YzM7XHJcbiRyZWQtMjAwOiAjZmFhMTliO1xyXG4kcmVkLTMwMDogI2Y3N2I3MjtcclxuJHJlZC00MDA6ICNmNjVmNTQ7XHJcbiRyZWQtNTAwOiAjZjQ0MzM2O1xyXG4kcmVkLTYwMDogI2YzM2QzMDtcclxuJHJlZC03MDA6ICNmMTM0Mjk7XHJcbiRyZWQtODAwOiAjZWYyYzIyO1xyXG4kcmVkLTkwMDogI2VjMWUxNjtcclxuXHJcbiRvcmFuZ2UtMTAwOiAjZmVkOGI5O1xyXG4kb3JhbmdlLTIwMDogI2ZlYmY4YTtcclxuJG9yYW5nZS0zMDA6ICNmZWE1NWI7XHJcbiRvcmFuZ2UtNDAwOiAjZmQ5MTM3O1xyXG4kb3JhbmdlLTUwMDogI2ZkN2UxNDtcclxuJG9yYW5nZS02MDA6ICNmZDc2MTI7XHJcbiRvcmFuZ2UtNzAwOiAjZmM2YjBlO1xyXG4kb3JhbmdlLTgwMDogI2ZjNjEwYjtcclxuJG9yYW5nZS05MDA6ICNmYzRlMDY7XHJcblxyXG4keWVsbG93LTEwMDogI2ZmZWNiNTtcclxuJHllbGxvdy0yMDA6ICNmZmUwODM7XHJcbiR5ZWxsb3ctMzAwOiAjZmZkNDUxO1xyXG4keWVsbG93LTQwMDogI2ZmY2EyYztcclxuJHllbGxvdy01MDA6ICNmZmMxMDc7XHJcbiR5ZWxsb3ctNjAwOiAjZmZiYjA2O1xyXG4keWVsbG93LTcwMDogI2ZmYjMwNTtcclxuJHllbGxvdy04MDA6ICNmZmFiMDQ7XHJcbiR5ZWxsb3ctOTAwOiAjZmY5ZTAyO1xyXG5cclxuJGdyZWVuLTEwMDogI2IzZWZjYjtcclxuJGdyZWVuLTIwMDogIzgwZTRhOTtcclxuJGdyZWVuLTMwMDogIzRkZDk4NztcclxuJGdyZWVuLTQwMDogIzI2ZDA2ZDtcclxuJGdyZWVuLTUwMDogIzAwYzg1MztcclxuJGdyZWVuLTYwMDogIzAwYzI0YztcclxuJGdyZWVuLTcwMDogIzAwYmI0MjtcclxuJGdyZWVuLTgwMDogIzAwYjQzOTtcclxuJGdyZWVuLTkwMDogIzAwYTcyOTtcclxuXHJcbiR0ZWFsLTEwMDogI2JjZWZlMDtcclxuJHRlYWwtMjAwOiAjOTBlNGNiO1xyXG4kdGVhbC0zMDA6ICM2M2Q5YjY7XHJcbiR0ZWFsLTQwMDogIzQxZDFhNztcclxuJHRlYWwtNTAwOiAjMjBjOTk3O1xyXG4kdGVhbC02MDA6ICMxY2MzOGY7XHJcbiR0ZWFsLTcwMDogIzE4YmM4NDtcclxuJHRlYWwtODAwOiAjMTNiNTdhO1xyXG4kdGVhbC05MDA6ICMwYmE5Njk7XHJcblxyXG4kY3lhbi0xMDA6ICNjNWVmZjM7XHJcbiRjeWFuLTIwMDogIzlmZTRlYjtcclxuJGN5YW4tMzAwOiAjNzhkOWUyO1xyXG4kY3lhbi00MDA6ICM1YmQxZGM7XHJcbiRjeWFuLTUwMDogIzNlYzlkNjtcclxuJGN5YW4tNjAwOiAjMzhjM2QxO1xyXG4kY3lhbi03MDA6ICMzMGJjY2M7XHJcbiRjeWFuLTgwMDogIzI4YjVjNjtcclxuJGN5YW4tOTAwOiAjMWJhOWJjO1xyXG5cclxuJHdoaXRlOiAjZmZmZmZmO1xyXG4kZ3JheS0xMDA6ICNmOGY5ZmE7XHJcbiRncmF5LTIwMDogI2U5ZWNlZjtcclxuJGdyYXktMzAwOiAjZGVlMmU2O1xyXG4kZ3JheS00MDA6ICNjZWQ0ZGE7XHJcbiRncmF5LTUwMDogI2FkYjViZDtcclxuJGdyYXktNjAwOiAjNmM3NTdkO1xyXG4kZ3JheS03MDA6ICM0OTUwNTc7XHJcbiRncmF5LTgwMDogIzM0M2E0MDtcclxuJGdyYXktOTAwOiAjMjEyNTI5O1xyXG4kYmxhY2s6ICMwMDAwMDA7XHJcbiRkYXJrOiAjMTExOTM2O1xyXG4iLCIvLyBCcmVha3BvaW50IHZpZXdwb3J0IHNpemVzIGFuZCBtZWRpYSBxdWVyaWVzLlxuLy9cbi8vIEJyZWFrcG9pbnRzIGFyZSBkZWZpbmVkIGFzIGEgbWFwIG9mIChuYW1lOiBtaW5pbXVtIHdpZHRoKSwgb3JkZXIgZnJvbSBzbWFsbCB0byBsYXJnZTpcbi8vXG4vLyAgICAoeHM6IDAsIHNtOiA1NzZweCwgbWQ6IDc2OHB4LCBsZzogOTkycHgsIHhsOiAxMjAwcHgsIHh4bDogMTQwMHB4KVxuLy9cbi8vIFRoZSBtYXAgZGVmaW5lZCBpbiB0aGUgYCRncmlkLWJyZWFrcG9pbnRzYCBnbG9iYWwgdmFyaWFibGUgaXMgdXNlZCBhcyB0aGUgYCRicmVha3BvaW50c2AgYXJndW1lbnQgYnkgZGVmYXVsdC5cblxuLy8gTmFtZSBvZiB0aGUgbmV4dCBicmVha3BvaW50LCBvciBudWxsIGZvciB0aGUgbGFzdCBicmVha3BvaW50LlxuLy9cbi8vICAgID4+IGJyZWFrcG9pbnQtbmV4dChzbSlcbi8vICAgIG1kXG4vLyAgICA+PiBicmVha3BvaW50LW5leHQoc20sICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCwgeHhsOiAxNDAwcHgpKVxuLy8gICAgbWRcbi8vICAgID4+IGJyZWFrcG9pbnQtbmV4dChzbSwgJGJyZWFrcG9pbnQtbmFtZXM6ICh4cyBzbSBtZCBsZyB4bCB4eGwpKVxuLy8gICAgbWRcbkBmdW5jdGlvbiBicmVha3BvaW50LW5leHQoJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMsICRicmVha3BvaW50LW5hbWVzOiBtYXAta2V5cygkYnJlYWtwb2ludHMpKSB7XG4gICRuOiBpbmRleCgkYnJlYWtwb2ludC1uYW1lcywgJG5hbWUpO1xuICBAaWYgbm90ICRuIHtcbiAgICBAZXJyb3IgXCJicmVha3BvaW50IGAjeyRuYW1lfWAgbm90IGZvdW5kIGluIGAjeyRicmVha3BvaW50c31gXCI7XG4gIH1cbiAgQHJldHVybiBpZigkbiA8IGxlbmd0aCgkYnJlYWtwb2ludC1uYW1lcyksIG50aCgkYnJlYWtwb2ludC1uYW1lcywgJG4gKyAxKSwgbnVsbCk7XG59XG5cbi8vIE1pbmltdW0gYnJlYWtwb2ludCB3aWR0aC4gTnVsbCBmb3IgdGhlIHNtYWxsZXN0IChmaXJzdCkgYnJlYWtwb2ludC5cbi8vXG4vLyAgICA+PiBicmVha3BvaW50LW1pbihzbSwgKHhzOiAwLCBzbTogNTc2cHgsIG1kOiA3NjhweCwgbGc6IDk5MnB4LCB4bDogMTIwMHB4LCB4eGw6IDE0MDBweCkpXG4vLyAgICA1NzZweFxuQGZ1bmN0aW9uIGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHM6ICRncmlkLWJyZWFrcG9pbnRzKSB7XG4gICRtaW46IG1hcC1nZXQoJGJyZWFrcG9pbnRzLCAkbmFtZSk7XG4gIEByZXR1cm4gaWYoJG1pbiAhPSAwLCAkbWluLCBudWxsKTtcbn1cblxuLy8gTWF4aW11bSBicmVha3BvaW50IHdpZHRoLlxuLy8gVGhlIG1heGltdW0gdmFsdWUgaXMgcmVkdWNlZCBieSAwLjAycHggdG8gd29yayBhcm91bmQgdGhlIGxpbWl0YXRpb25zIG9mXG4vLyBgbWluLWAgYW5kIGBtYXgtYCBwcmVmaXhlcyBhbmQgdmlld3BvcnRzIHdpdGggZnJhY3Rpb25hbCB3aWR0aHMuXG4vLyBTZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL21lZGlhcXVlcmllcy00LyNtcS1taW4tbWF4XG4vLyBVc2VzIDAuMDJweCByYXRoZXIgdGhhbiAwLjAxcHggdG8gd29yayBhcm91bmQgYSBjdXJyZW50IHJvdW5kaW5nIGJ1ZyBpbiBTYWZhcmkuXG4vLyBTZWUgaHR0cHM6Ly9idWdzLndlYmtpdC5vcmcvc2hvd19idWcuY2dpP2lkPTE3ODI2MVxuLy9cbi8vICAgID4+IGJyZWFrcG9pbnQtbWF4KG1kLCAoeHM6IDAsIHNtOiA1NzZweCwgbWQ6IDc2OHB4LCBsZzogOTkycHgsIHhsOiAxMjAwcHgsIHh4bDogMTQwMHB4KSlcbi8vICAgIDc2Ny45OHB4XG5AZnVuY3Rpb24gYnJlYWtwb2ludC1tYXgoJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1heDogbWFwLWdldCgkYnJlYWtwb2ludHMsICRuYW1lKTtcbiAgQHJldHVybiBpZigkbWF4IGFuZCAkbWF4ID4gMCwgJG1heCAtIC4wMiwgbnVsbCk7XG59XG5cbi8vIFJldHVybnMgYSBibGFuayBzdHJpbmcgaWYgc21hbGxlc3QgYnJlYWtwb2ludCwgb3RoZXJ3aXNlIHJldHVybnMgdGhlIG5hbWUgd2l0aCBhIGRhc2ggaW4gZnJvbnQuXG4vLyBVc2VmdWwgZm9yIG1ha2luZyByZXNwb25zaXZlIHV0aWxpdGllcy5cbi8vXG4vLyAgICA+PiBicmVha3BvaW50LWluZml4KHhzLCAoeHM6IDAsIHNtOiA1NzZweCwgbWQ6IDc2OHB4LCBsZzogOTkycHgsIHhsOiAxMjAwcHgsIHh4bDogMTQwMHB4KSlcbi8vICAgIFwiXCIgIChSZXR1cm5zIGEgYmxhbmsgc3RyaW5nKVxuLy8gICAgPj4gYnJlYWtwb2ludC1pbmZpeChzbSwgKHhzOiAwLCBzbTogNTc2cHgsIG1kOiA3NjhweCwgbGc6IDk5MnB4LCB4bDogMTIwMHB4LCB4eGw6IDE0MDBweCkpXG4vLyAgICBcIi1zbVwiXG5AZnVuY3Rpb24gYnJlYWtwb2ludC1pbmZpeCgkbmFtZSwgJGJyZWFrcG9pbnRzOiAkZ3JpZC1icmVha3BvaW50cykge1xuICBAcmV0dXJuIGlmKGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHMpID09IG51bGwsIFwiXCIsIFwiLSN7JG5hbWV9XCIpO1xufVxuXG4vLyBNZWRpYSBvZiBhdCBsZWFzdCB0aGUgbWluaW11bSBicmVha3BvaW50IHdpZHRoLiBObyBxdWVyeSBmb3IgdGhlIHNtYWxsZXN0IGJyZWFrcG9pbnQuXG4vLyBNYWtlcyB0aGUgQGNvbnRlbnQgYXBwbHkgdG8gdGhlIGdpdmVuIGJyZWFrcG9pbnQgYW5kIHdpZGVyLlxuQG1peGluIG1lZGlhLWJyZWFrcG9pbnQtdXAoJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1pbjogYnJlYWtwb2ludC1taW4oJG5hbWUsICRicmVha3BvaW50cyk7XG4gIEBpZiAkbWluIHtcbiAgICBAbWVkaWEgKG1pbi13aWR0aDogJG1pbikge1xuICAgICAgQGNvbnRlbnQ7XG4gICAgfVxuICB9IEBlbHNlIHtcbiAgICBAY29udGVudDtcbiAgfVxufVxuXG4vLyBNZWRpYSBvZiBhdCBtb3N0IHRoZSBtYXhpbXVtIGJyZWFrcG9pbnQgd2lkdGguIE5vIHF1ZXJ5IGZvciB0aGUgbGFyZ2VzdCBicmVha3BvaW50LlxuLy8gTWFrZXMgdGhlIEBjb250ZW50IGFwcGx5IHRvIHRoZSBnaXZlbiBicmVha3BvaW50IGFuZCBuYXJyb3dlci5cbkBtaXhpbiBtZWRpYS1icmVha3BvaW50LWRvd24oJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1heDogYnJlYWtwb2ludC1tYXgoJG5hbWUsICRicmVha3BvaW50cyk7XG4gIEBpZiAkbWF4IHtcbiAgICBAbWVkaWEgKG1heC13aWR0aDogJG1heCkge1xuICAgICAgQGNvbnRlbnQ7XG4gICAgfVxuICB9IEBlbHNlIHtcbiAgICBAY29udGVudDtcbiAgfVxufVxuXG4vLyBNZWRpYSB0aGF0IHNwYW5zIG11bHRpcGxlIGJyZWFrcG9pbnQgd2lkdGhzLlxuLy8gTWFrZXMgdGhlIEBjb250ZW50IGFwcGx5IGJldHdlZW4gdGhlIG1pbiBhbmQgbWF4IGJyZWFrcG9pbnRzXG5AbWl4aW4gbWVkaWEtYnJlYWtwb2ludC1iZXR3ZWVuKCRsb3dlciwgJHVwcGVyLCAkYnJlYWtwb2ludHM6ICRncmlkLWJyZWFrcG9pbnRzKSB7XG4gICRtaW46IGJyZWFrcG9pbnQtbWluKCRsb3dlciwgJGJyZWFrcG9pbnRzKTtcbiAgJG1heDogYnJlYWtwb2ludC1tYXgoJHVwcGVyLCAkYnJlYWtwb2ludHMpO1xuXG4gIEBpZiAkbWluICE9IG51bGwgYW5kICRtYXggIT0gbnVsbCB7XG4gICAgQG1lZGlhIChtaW4td2lkdGg6ICRtaW4pIGFuZCAobWF4LXdpZHRoOiAkbWF4KSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH0gQGVsc2UgaWYgJG1heCA9PSBudWxsIHtcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKCRsb3dlciwgJGJyZWFrcG9pbnRzKSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH0gQGVsc2UgaWYgJG1pbiA9PSBudWxsIHtcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LWRvd24oJHVwcGVyLCAkYnJlYWtwb2ludHMpIHtcbiAgICAgIEBjb250ZW50O1xuICAgIH1cbiAgfVxufVxuXG4vLyBNZWRpYSBiZXR3ZWVuIHRoZSBicmVha3BvaW50J3MgbWluaW11bSBhbmQgbWF4aW11bSB3aWR0aHMuXG4vLyBObyBtaW5pbXVtIGZvciB0aGUgc21hbGxlc3QgYnJlYWtwb2ludCwgYW5kIG5vIG1heGltdW0gZm9yIHRoZSBsYXJnZXN0IG9uZS5cbi8vIE1ha2VzIHRoZSBAY29udGVudCBhcHBseSBvbmx5IHRvIHRoZSBnaXZlbiBicmVha3BvaW50LCBub3Qgdmlld3BvcnRzIGFueSB3aWRlciBvciBuYXJyb3dlci5cbkBtaXhpbiBtZWRpYS1icmVha3BvaW50LW9ubHkoJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1pbjogIGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHMpO1xuICAkbmV4dDogYnJlYWtwb2ludC1uZXh0KCRuYW1lLCAkYnJlYWtwb2ludHMpO1xuICAkbWF4OiAgYnJlYWtwb2ludC1tYXgoJG5leHQsICRicmVha3BvaW50cyk7XG5cbiAgQGlmICRtaW4gIT0gbnVsbCBhbmQgJG1heCAhPSBudWxsIHtcbiAgICBAbWVkaWEgKG1pbi13aWR0aDogJG1pbikgYW5kIChtYXgtd2lkdGg6ICRtYXgpIHtcbiAgICAgIEBjb250ZW50O1xuICAgIH1cbiAgfSBAZWxzZSBpZiAkbWF4ID09IG51bGwge1xuICAgIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAoJG5hbWUsICRicmVha3BvaW50cykge1xuICAgICAgQGNvbnRlbnQ7XG4gICAgfVxuICB9IEBlbHNlIGlmICRtaW4gPT0gbnVsbCB7XG4gICAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC1kb3duKCRuZXh0LCAkYnJlYWtwb2ludHMpIHtcbiAgICAgIEBjb250ZW50O1xuICAgIH1cbiAgfVxufVxuIl19 */
